import BaseEventHandler from './BaseEventHandler'
import { getDefaultPreferences, sortFriendList } from '../common_base'

/**
 * 用户事件处理器
 * 处理用户相关的事件，如用户信息更新、好友申请等
 */
class UserEventHandler extends BaseEventHandler {
    /**
     * 初始化用户相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on("userResponseFriendApply", (data) => {
            this.friendApplyResponse(data)
        })

        controller.on("userAddFriend", (data) => {
            this.notifyAddFriend(data.friendInfo)
        })

        controller.on("userApplyAddFriend", (data) => {
            this.setFriendApplys(data)
        })

        controller.on("notify_add_friend", (data) => {
            this.notifyAddFriend(data)
        })

        controller.on("update_friend_info", (data) => {
            this.updateFriendInfo(data)
        })

        controller.on("notify_friend_destroy", (data) => {
            this.updateFriendD<PERSON>roy(data)
        })

        controller.on("update_user_info", (data) => {
            this.onUpdateUserInfo(data)
        })

        controller.on("update_user_portrait_img", (data) => {
            this.onUpdateUserPortraitInfo(data)
        })

        controller.on("notify_login_another", () => {
            this.notifyLoginAnother()
        })

        controller.on("notify_user_destroy", () => {
            this.notifyUserDestroy()
        })

        controller.on('user_info', (is_succ, info) => {
            info.preferences = getDefaultPreferences(info)
            this.vm.$store.commit('user/updateUser', info)
            this.vm.checkProfessionalIdentity(info)
        })

        controller.on('version_info', (info) => {
            window.server_info = info
        })
    }

    friendApplyResponse(data){
        if (data.isAgree) {
            var str=this.vm.lang.response_accept_friend.replace('${1}',data.userInfo.nickname)
        }else{
            var str=this.vm.lang.response_disaccept_friend.replace('${1}',data.userInfo.nickname);
            this.vm.$store.commit('relationship/removeApplyingList',data.userInfo)
        }
        this.vm.$message.success(str)
    }

    notifyAddFriend(friend){
        var list =[]
        list.push(friend)
        this.vm.$store.commit('friendList/addFriendList',list[0]);
        this.vm.$store.commit('relationship/removeApplyingList',list[0])
        sortFriendList();
    }

    setFriendApplys(data){
        let json={}
        json.param=data.friendInfo;
        json.notify_id=data.applyInfo.id;
        json.description=data.applyInfo.msg_body.description;
        this.vm.$store.commit('notifications/addFriendApply',json);
    }

    updateFriendInfo(data){
        //更新好友信息
        for(let hospital of this.vm.$store.state.dynamicGlobalParams.hospitals){
            if (data.hospital_id==hospital.id) {
                data.hospital_name=hospital.hospital_name;
                break;
            }
        }
        this.vm.$store.commit('friendList/updateFriendToFriendList',data)
        this.vm.$store.commit('chatList/updateFriendToChatList',data)
        this.vm.$store.commit('conversationList/updateFriendToConversationList',data)
        sortFriendList();
        // this.vm.$store.commit('conversationList/updateFriendToAttendeeList',data)
    }

    updateFriendDestroy(data){
        this.updateFriendInfo(data);
        this.vm.$store.commit('conversationList/updateFriendToAttendeeList',data)
    }

    onUpdateUserInfo(data){
        //更新用户信息
        this.vm.changeDefaultImg(data);
        this.vm.$store.commit('user/updateUser',data);
        this.vm.$store.commit('chatList/updateFriendToChatList', this.vm.user);
        this.vm.$store.commit('conversationList/updateFriendToConversationList',this.vm.user);
    }
    onUpdateUserPortraitInfo(data){
        //更新用户头像信息
        // let path_local=data.avatar
        this.vm.$store.commit('user/updateUser',{
            avatar:data.avatar,
            avatar_local:data.avatar_local
        });
        this.vm.$store.commit('conversationList/updateFriendToAttendeeList',{
            avatar:data.avatar,
            avatar_local:data.avatar_local,
            id:this.vm.user.id,
            state:this.vm.user.state,
            nickname:this.vm.user.nickname
        });
    }

    notifyLoginAnother(){
        window.Logger.save({
            message:'notifyLoginAnother',
            eventType: `socket_event`,
        });
        this.vm.resetApp();
        setTimeout(()=>{
            this.vm.clearAndDirectToLogin(this.vm.lang.login_another)
        },1500)

    }
    notifyUserDestroy(){
        window.Logger.save({
            message:'notifyUserDestroy',
            eventType: `socket_event`,
        });
        this.vm.resetApp();
        setTimeout(()=>{
            this.vm.clearAndDirectToLogin(this.vm.lang.account_destroy_tip)
        },1500)
    }
}

export default UserEventHandler
