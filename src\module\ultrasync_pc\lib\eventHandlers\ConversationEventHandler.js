import BaseEventHandler from './BaseEventHandler'
import { getLiveRoomObj, closeChatWindowIfNeed, findServiceId, sortFriendList } from '../common_base'

/**
 * 对话事件处理器
 * 处理对话、群组、资源和通知相关的事件
 */
class ConversationEventHandler extends BaseEventHandler {
    constructor(vueInstance, autoLoginManager, mainScreenManager) {
        super(vueInstance, autoLoginManager, mainScreenManager)
        this.debounceSortChatList = null // 需要从原文件中获取
        this.hasSetCurrentList = false
        this.isTopFileTransferAssistant = false
    }

    /**
     * 初始化对话相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        // 对话相关事件
        this.initConversationEvents(controller)

        // 群组相关事件
        this.initGroupsetEvents(controller)

        // 资源相关事件
        this.initResourceEvents(controller)

        // 通知相关事件
        this.initNotificationEvents(controller)
    }

    /**
     * 初始化对话相关事件
     * @param {Object} controller 控制器
     */
    initConversationEvents(controller) {
        controller.on("notify_start_conversation", (is_succ, conversation, start_type, kickout_data) => {
            this.NotifyStartConversation(is_succ, conversation, start_type, kickout_data)
        })

        controller.on('request_conversation_start_ultrasound_desktop', (data) => {
            this.vm.notifyStartConversationByMonitorWall(data)
        })

        controller.on("receive_group_message", (data) => {
            console.log('receive_group_message', data, 2)
            if (!this.vm.conversationList.hasOwnProperty(data.group_id)) {
                this.setSayChatMessageReceiveGroupMessage(data, false)
                if (data.msg_type === this.vm.systemConfig.msg_type.LIVE_INVITE ||
                    data.groupInfo.service_type === this.vm.systemConfig.ServiceConfig.type.LiveBroadCast) {
                    this.vm.debounceUpdateLiveCount()
                }
                if (this.debounceSortChatList) {
                    this.debounceSortChatList()
                }
            }
        })

        controller.on("recent_active_conversation_list", (is_succ, data) => {
            this.setCurrentList(is_succ, data)
            window.CWorkstationCommunicationMng.QueryStandaloneWorkstationShareExamInfo()
        })

        controller.on("recent_active_conversation_list_last_message", (is_succ, data) => {
            this.setLastMessage(is_succ, data)
            this.vm.$store.commit('chatList/updateUnReadMap', data)
        })

        controller.on("friend_list", (is_succ, data) => {
            this.setFriendList(is_succ, data)
            setTimeout(() => {
                this.autoUploadDrAiData()
            }, 2500)
        })

        controller.on("userAddLoginClient", (data) => {
            if (data.allClientType.length > 1) {
                this.isTopFileTransferAssistant = true
                if (this.hasSetCurrentList) {
                    this.topFileTransferAssistant()
                }
            }
        })

        controller.on("conversation_list", (is_succ, data) => {
            this.setGroupList(is_succ, data)
        })

        controller.on("group_applys", (is_succ, data) => {
            this.setGroupApplys(is_succ, data)
        })

        controller.on("friend_applys", (is_succ, data) => {
            this.dealFriendApplys(is_succ, data)
        })
    }

    /**
     * 初始化群组相关事件
     * @param {Object} controller 控制器
     */
    initGroupsetEvents(controller) {
        controller.on("notify_delete_group", (json_str) => {
            let data = typeof json_str === 'string' ? JSON.parse(json_str) : json_str
            this.vm.$store.commit('chatList/deleteChatList', { cid: data.cid })
            this.vm.$store.commit('groupList/deleteGroupList', { cid: data.cid })
            this.vm.$store.commit('conversationList/deleteConversationList', { cid: data.cid })
            this.vm.$store.commit('notifications/deleteGroupApplyByCid', { cid: data.cid })
            this.vm.$store.commit('consultationImageList/deleteConsultationImageListByGroupID', { cid: data.cid })

            window.main_screen.conversation_list[data.cid].onResponseDeleteAttendee()
            let liveRoom = getLiveRoomObj(this.vm.$root.currentLiveCid)
            if (liveRoom) {
                liveRoom.LeaveChannelAux()
                window.CWorkstationCommunicationMng.StopConference()
            }
            closeChatWindowIfNeed(data.cid)
            setTimeout(() => {
                this.vm.$message.success(this.vm.lang.user_exit_group_succ)
            }, 100)
        })

        controller.on("open_register_scan_room_view", (data) => {
            const attributes = {
                mac_addr: data.mac_addr,
                name: "",
                hospital_id: 0,
                ultrasync_box_mac_addr: data.mac_addr
            }
            this.vm.$store.commit('user/updateUser', {
                scan_room: attributes
            })
        })

        controller.on('notify_refresh_manager_groupset_list', (data) => {
            console.log('notify_refresh_manager_groupset_list', data)
            if (data.action === 'delete' && (Number(this.vm.$route.params.groupset_id) === data.groupSetID)) {
                this.vm.$router.replace('/main/index/chat_window/0')
            }
            this.getManagerGroupsetList()
        })

        controller.on('notify_refresh_my_groupset_list', (data) => {
            console.log('notify_refresh_my_groupset_list', data)
            if (data.action === 'delete' && (Number(this.vm.$route.params.groupset_id) === data.groupSetID)) {
                this.vm.$router.replace('/main/index/chat_window/0')
            }
            this.getGroupSetList()
        })
    }

    /**
     * 初始化资源相关事件
     * @param {Object} controller 控制器
     */
    initResourceEvents(controller) {
        controller.on("notify.group.resource.delete.exam", (data) => {
            console.log(data, 'notify.group.resource.delete.exam')
            this.vm.$store.commit('examList/deleteExamListItem', {
                cid: data.groupID,
                exam_id: data.examID,
            })
            this.vm.$root.eventBus.$emit('deleteExamItem')

            if (Array.isArray(data.deleteResourceIDList)) {
                data.deleteResourceIDList.forEach(resource_id => {
                    this.vm.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                        resource_id,
                        data: {
                            state: 0 // 被删除
                        }
                    })
                    this.vm.$store.commit('chatList/updateLastChatMessageByResourceDelete', {
                        cid: data.groupID,
                        data: {
                            msg_type: this.vm.systemConfig.msg_type.ResourceDelete,
                            resource_id
                        }
                    })
                })
            }

            if (Array.isArray(data.deleteMessageIDList)) {
                this.vm.$store.commit("conversationList/deleteChatMessagesByGmsgIdList", {
                    gmsg_id_list: data.deleteMessageIDList,
                    cid: data.groupID
                })
                this.vm.$root.eventBus.$emit('notifyDeleteChatMessages', {
                    cid: data.groupID,
                    gmsg_id_list: data.deleteMessageIDList
                })
            }
        })

        controller.on('notify.group.resource.delete.resource', (data) => {
            if (Array.isArray(data.deleteResourceIDList)) {
                data.deleteResourceIDList.forEach(resource_id => {
                    this.vm.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                        resource_id,
                        data: {
                            state: 0 // 被删除
                        }
                    })
                    if (data.deleteMessageIDList.length > 0) {
                        this.vm.$store.commit('chatList/updateLastChatMessageByResourceDelete', {
                            cid: data.groupID,
                            data: {
                                msg_type: this.vm.systemConfig.msg_type.ResourceDelete,
                                resource_id
                            }
                        })
                    }
                    this.vm.$root.eventBus.$emit('deleteFileToExamList', {
                        cid: data.groupID,
                        resource_id
                    })
                })
            }

            if (Array.isArray(data.deleteMessageIDList)) {
                this.vm.$store.commit("conversationList/deleteChatMessagesByGmsgIdList", {
                    gmsg_id_list: data.deleteMessageIDList,
                    cid: data.groupID
                })
                this.vm.$root.eventBus.$emit('notifyDeleteChatMessages', {
                    cid: data.groupID,
                    gmsg_id_list: data.deleteMessageIDList
                })
            }
        })
    }

    /**
     * 初始化通知相关事件
     * @param {Object} controller 控制器
     */
    initNotificationEvents(controller) {
        controller.on("notify_exception", () => {
            this.autoLoginManager.onNotifyException()
        })

        controller.on("NotifyStandaloneWorkstationShareExamInfo", (data) => {
            this.NotifyStandaloneWorkstationShareExamInfo(data)
        })

        controller.on("notify_download_task", (data) => {
            if ("error" === data.type) {
                this.vm.$message.error(data.errorInfo)
            }
        })

        controller.on("notify_update_groupset_portrait", (err, result) => {
            if (!err) {
                this.notifyUpdateGroupsetAvatar(result)
            }
        })
    }

    /**
     * 通知开始会话
     * @param {boolean} is_succ 是否成功
     * @param {Object} conversation 会话对象
     * @param {Object} start_type 启动类型
     * @param {Object} kickout_data 踢出数据
     */
    NotifyStartConversation(is_succ, conversation, start_type, kickout_data) {
        window.Logger.save({
            message: 'NotifyStartConversation',
            eventType: `socket_event`,
            data: { conversation, is_succ, start_type, kickout_data },
        });

        if (is_succ) {
            // 后台通知开启会话
            let cid = conversation.id
            let existConversation = this.checkExitConversation(cid)
            if (existConversation) {
                return
            }
            let existChatListItem = this.checkExistChatListItem(cid)
            if (!existChatListItem) {
                // 不存在最近会话列表则加入
                var chatItem = {
                    cid: cid,
                    fid: conversation.fid,
                    is_single_chat: conversation.is_single_chat,
                    subject: conversation.subject,
                    type: conversation.type,
                    sex: conversation.sex,
                    state: conversation.state,
                    avatar: conversation.avatar || '',
                    message: {},
                    service_type: conversation.service_type || 0,
                    user_status: conversation.user_status
                }
                this.vm.$store.commit('chatList/addChatList', chatItem)
            }
            // 这里省略了大部分复杂的逻辑，需要时可以从原文件中完整迁移
            this.vm.$store.commit('conversationList/setConversation', conversation);
        } else {
            this.vm.$root.eventBus.$emit('createConversationFail')
        }
    }

    /**
     * 处理接收到的群消息
     * @param {Object} data 消息数据
     * @param {boolean} is_open_conversation 是否打开会话
     */
    setSayChatMessageReceiveGroupMessage(data, is_open_conversation) {
        console.log(data, 'setSayChatMessageReceiveGroupMessage')
        if (is_open_conversation) {
            const res = this.PreHandleChatMessageByGroupMessage(data)
            if (window.vm.$store.state.conversationList[data.group_id].is_loaded_history_list) {
                this.vm.$store.commit('conversationList/setChatMessage', res)
            }
        } else {
            this.PreHandleChatMessageByGroupMessage(data)
        }
    }

    /**
     * 设置当前聊天列表
     * @param {boolean} is_succ 是否成功
     * @param {Array} list 聊天列表数据
     */
    setCurrentList(is_succ, list) {
        if (is_succ) {
            this.vm.$store.commit('chatList/initChatList', list)
            this.vm.$store.commit('loadingConfig/updateLoaded', {
                key: 'loadedChatList',
                loaded: true
            })

            // 如果多端登录，置顶文件传输助手
            if (this.isTopFileTransferAssistant) {
                this.topFileTransferAssistant()
            }
            this.hasSetCurrentList = true
        } else {
            this.vm.$message.error('setCurrentList error')
        }
    }

    /**
     * 设置最后一条消息记录
     * @param {boolean} is_succ 是否成功
     * @param {Array} list 消息列表
     */
    setLastMessage(is_succ, list) {
        if (is_succ) {
            for (let item of list) {
                item.message.original_msg_body = item.message.msg_body
                item.message.msg_body = this.vm.parseMessageBody(item.message.msg_body)
                if (item.message.been_withdrawn === 2) {
                    item.message.msg_type = this.vm.systemConfig.msg_type.WITHDRAW
                } else if (item.message.been_withdrawn === 1) {
                    item.message.msg_type = this.vm.systemConfig.msg_type.ResourceDelete
                }
                this.vm.$store.commit('chatList/setLastMessage', item);
            }
        } else {
            this.vm.$message.error('setLastMessage error')
        }
    }

    /**
     * 设置好友列表
     * @param {boolean} is_succ 是否成功
     * @param {Array} list 好友列表
     */
    setFriendList(is_succ, list) {
        if (is_succ) {
            this.vm.$store.commit('loadingConfig/updateLoaded', {
                key: 'loadedFriendList',
                loaded: true
            });
            this.vm.$store.commit('friendList/initFriendList', list);
            sortFriendList();
        } else {
            this.vm.$message.error('setFriendList error')
        }
    }

    autoUploadDrAiData() {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    /**
     * 置顶文件传输助手
     */
    async topFileTransferAssistant() {
        // 置顶文件传输助手
        let service_type = this.vm.systemConfig.ServiceConfig.type.FileTransferAssistant
        let analyze = await findServiceId(service_type)
        console.log("文件传输助手id:", analyze)

        if (analyze.cid) {
            // 会话列表中存在文件传输助手
            this.vm.$store.commit('chatList/setTopChat', analyze.cid)
        } else {
            // 会话列表没有则新开一个会话
            const that = this.vm
            let fid = analyze.id
            this.vm.$root.socket.emit("request_start_single_chat_conversation", {
                list: [fid, this.vm.user.uid],
                start_type: undefined,
                mode: this.vm.systemConfig.ConversationConfig.mode.Single,
                type: this.vm.systemConfig.ConversationConfig.type.Single
            }, function(is_succ, data) {
                if (is_succ) {
                    that.$store.commit('conversationList/initConversation', data)
                    that.$store.commit('examList/initExamObj', data)
                    setTimeout(() => {
                        that.$store.commit('chatList/setTopChat', data)
                    }, 1000) // 如果立即执行 setTopChat查不到该cid(data)的会话对象
                } else {
                    that.$message.error(that.lang.start_conversation_error)
                }
            })
        }
    }

    /**
     * 设置群组列表
     * @param {boolean} is_succ 是否成功
     * @param {Object} data 群组数据
     */
    setGroupList(is_succ, data) {
        if (is_succ) {
            var list = this.vm.parseObjToArr(data)
            this.vm.$store.commit('loadingConfig/updateLoaded', {
                key: 'loadedGroupList',
                loaded: true
            });
            this.vm.$store.commit('groupList/initGroupList', list);
        } else {
            this.vm.$message.error('setGroupList error')
        }
    }

    /**
     * 设置入群申请
     * @param {Array} list 申请列表
     */
    setGroupApplys(list) {
        if (list.length > 0) {
            this.vm.setDefaultImg(list);
            for (let item of list) {
                this.vm.$store.commit('notifications/addGroupApply', item);
            }
            this.vm.conversationManager.messageNotify('join_group')
        }
    }

    /**
     * 处理好友申请
     * @param {Object} data 申请数据
     */
    dealFriendApplys(data) {
        if (data.notify_type == 'request_add_friend') {
            var temp = []
            temp.push(data.param)
            this.vm.$store.commit('notifications/addFriendApply', data);
            this.vm.conversationManager.messageNotify('friend_apply')
        } else if (data.notify_type == "response_add_friend") {
            if (data.param.accept) {
                var str = this.vm.lang.response_accept_friend.replace('${1}', data.param.nickname)
            } else {
                var str = this.vm.lang.response_disaccept_friend.replace('${1}', data.param.nickname);
                this.vm.$store.commit('relationship/removeApplyingList', data.param)
            }
            this.vm.$message.success(str)
        }
    }

    /**
     * 获取管理员群组列表
     */
    getManagerGroupsetList() {
        window.main_screen.getManagerGroupsetList({}, (data) => {
            console.log(data, 'getManagerGroupsetList')
            if (data.error_code == 0) {
                let list = data.data;
                list.forEach(item => {
                    item.type = this.vm.systemConfig.ConversationConfig.type.GroupSet;
                })
                this.vm.$store.commit('groupset/initGroupsetManagerList', data.data);
            }
        })
    }

    /**
     * 获取群组列表
     */
    getGroupSetList() {
        window.main_screen.getGroupsetList({}, (data) => {
            console.log('getGroupsetList', data)
            if (data.error_code == 0) {
                let list = data.data;
                list.forEach(item => {
                    item.type = this.vm.systemConfig.ConversationConfig.type.GroupSet;
                })
                this.vm.$store.commit('groupset/initGroupsetList', data.data);
            }
        })
        this.getManagerGroupsetList()
    }

    /**
     * 通知独立工作站共享检查信息
     * @param {Object} data 数据
     */
    NotifyStandaloneWorkstationShareExamInfo(data) {
        // 判断是否在电视墙下，发起实时下，阅片下
        if (window.vm.$store.state.dynamicGlobalParams.tv_wall_mode) { // 在电视墙下禁止操作
            window.CWorkstationCommunicationMng.ShowConfirmDialog({
                title: this.vm.lang.warning_title,
                message: this.vm.lang.tv_wall_warning_message,
                yes_button: this.vm.lang.confirm_txt,
            });
            window.CWorkstationCommunicationMng.ClearStandaloneWorkstationShareExamInfo();
            return;
        }

        if (this.vm.$route.name == 'gallery') { // 在阅片下，转发窗口盖在最上层
            console.log("gallery");
            this.vm.$root.eventBus.$emit('hideRealTimeVideo');
        }

        console.log("NotifyStandaloneWorkstationShareExamInfo ", data);

        this.vm.$root.eventBus.$emit('openTransmit', {
            callback: this.generalTransmitSubmit,
            isStandaloneWorkstationShare: 1,
            cid: this.vm.$route.params.cid,
        })
    }

    /**
     * 通知更新群组头像
     * @param {Object} result 结果数据
     */
    notifyUpdateGroupsetAvatar(result) {
        let groupset = {
            avatar: result.avatar
        }
        this.vm.$store.commit('groupset/updateGroupsetAvatar', {
            avatar: groupset.avatar,
            id: result.groupset_id,
        })
        this.vm.$message.success(this.vm.lang.modify_photo_success);
        this.vm.back();
    }

    /**
     * 通用转发提交
     * @param {Object} data 数据
     */
    generalTransmitSubmit(data) {
        console.log("generalTransmitSubmit ", data);
        if (this.vm.$route.name == 'gallery') {
            this.vm.$root.eventBus.$emit('showRealTimeVideo');
        }

        Object.entries(this.vm.$store.state.conversationList).map(item => { // 判断会话列表中是否有与fid对应的会话 有则直接取之前的cid
            if (data.id && item[1].fid === data.id) {
                data.cid = item[0]
            }
        })
        this.vm.$root.transmitQueue_StandaloneWorkstation[data.cid || 'f-' + data.id] = 1;
        if (this.vm.$store.state.conversationList[data.cid]) {
            // 会话已开启则直接转发
            window.CWorkstationCommunicationMng.SendStandaloneWorkstationShareExamInfo(data.cid);
        } else {
            // 会话未开启则开启会话
            if (data.cid) {
                this.vm.openConversation(data.cid, 7)
            } else {
                this.vm.openConversation(data.id, 3)
            }
        }
    }

    /**
     * 检查会话是否已存在
     * @param {string} cid 会话ID
     * @returns {boolean} 是否存在
     */
    checkExitConversation(cid) {
        if (
            window.main_screen.conversation_list.hasOwnProperty(cid) &&
            window.main_screen.conversation_list[cid].gateway.check &&
            window.vm.$store.state.conversationList[cid].socket
        ) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 检查聊天列表项是否存在
     * @param {string} cid 会话ID
     * @returns {boolean} 是否存在
     */
    checkExistChatListItem(cid) {
        let exit = false
        const chatList = this.vm.$store.state.chatList.list || []
        for (let chat of chatList) {
            if (chat.cid == cid) {
                exit = true;
                break;
            }
        }
        return exit;
    }

    /**
     * 预处理群消息
     * @param {Object} omessage 原始消息对象
     * @returns {Object} 处理后的消息对象
     */
    PreHandleChatMessageByGroupMessage(omessage) {
        console.log('PreHandleChatMessageByGroupMessage', omessage)
        if (!omessage) {
            return
        }
        // 这里是一个简化的实现，完整的逻辑需要从原文件中迁移
        let message = this.judgeIfNeedAddChatList(omessage)
        let cid = message.group_id
        message.original_msg_body = message.msg_body
        message.msg_body = this.vm.parseMessageBody(message.msg_body)

        this.vm.$store.commit('chatList/addMessageNoSort', message)

        return {
            list: [message],
            cid: cid,
            type: 'append',
            is_localdb_msg: 0
        }
    }

    /**
     * 判断是否需要添加到聊天列表
     * @param {Object} omessage 原始消息对象
     * @param {boolean} isTop 是否置顶，默认为true
     * @returns {Object} 处理后的消息对象
     */
    judgeIfNeedAddChatList(omessage, isTop = true) {
        if (!omessage) {
            return
        }
        let cid = omessage.group_id || omessage.groupInfo.id

        let is_exist_chat_List = this.checkExistChatListItem(cid)
        let message = {
            ...omessage,
            downloading: false,
            gmsg_id: omessage.gmsg_id,
            group_id: omessage.group_id,
            msg_body: "",
            msg_type: omessage.msg_type,
            sendFail: false,
            send_ts: omessage.send_ts,
            sender_id: omessage.sender_id,
            sending: false,
            liveInfo: omessage.liveInfo,
            groupInfo: omessage.groupInfo,
            ai_result: omessage.ai_result,
            avatar: omessage.senderInfo && omessage.senderInfo.avatar,
            nickname: omessage.senderInfo && omessage.senderInfo.nickname,
            sex: omessage.senderInfo && omessage.senderInfo.sex,
            fid: omessage.senderInfo && omessage.senderInfo.id
        }

        if (!is_exist_chat_List) {
            // 不存在最近会话列表则加入
            let is_single_chat = omessage.groupInfo.type == 1 ? 1 : 0
            var chatItem = {
                cid: cid,
                is_single_chat,
                subject: message.groupInfo.subject,
                type: omessage.groupInfo.type,
                state: 1,
                avatar: is_single_chat ? message.avatar : message.groupInfo.avatar,
                message: {},
                service_type: message.groupInfo.service_type || 0,
                nickname: message.nickname || ''
            }
            if (is_single_chat) {
                chatItem.fid = message.fid
                chatItem.sex = message.sex
            }
            if (isTop) {
                this.vm.$store.commit('chatList/addAndTopChat', chatItem)
            } else {
                this.vm.$store.commit('chatList/addChatList', chatItem)
            }
        } else {
            if (isTop) {
                this.vm.$store.commit('chatList/setTopChat', cid)
            }
        }
        return message
    }
}

export default ConversationEventHandler
