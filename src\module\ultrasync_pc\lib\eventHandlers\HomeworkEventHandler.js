import BaseEventHandler from './BaseEventHandler'
import service from '../../service/service'

/**
 * 作业事件处理器
 * 处理作业相关的事件
 */
class HomeworkEventHandler extends BaseEventHandler {
    /**
     * 初始化作业相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on('student_answer_sheet_update', (data) => {
            if (data.type === 'add') {
                if (this.vm.$store.state.homework.globalUnfinish === 0) {
                    this.getUnfinishedHomework(0)
                }
            } else {
                this.getUnfinishedHomework(0)
            }

            data.gidList.forEach(cid => {
                if (this.vm.$store.state.conversationList[cid]) {
                    this.getUnfinishedHomework(cid)
                }
            })

            this.getCorrectedHomework(0)
            data.gidList.forEach(cid => {
                if (this.vm.$store.state.conversationList[cid]) {
                    this.getCorrectedHomework(cid)
                }
            })
        })

        controller.on('teacher_answer_sheet_update', (data) => {
            if (data.type === 'add') {
                this.getUncorrectHomework(0)
            } else {
                this.getUncorrectHomework(0)
            }

            data.gidList.forEach(cid => {
                if (this.vm.$store.state.conversationList[cid]) {
                    this.getUncorrectHomework(cid)
                }
            })
        })

        controller.on('update_ai_analyze_report', () => {
            // console.error('update_ai_analyze_report')
        })
    }

    /**
     * 获取已批改作业
     * @param {number} cid 会话ID
     */
    getCorrectedHomework(cid) {
        service.getFinishedList({
            gid: cid,
            page: 1,
            pageSize: 30,
            // 不使用status参数，改为在获取数据后过滤
        }).then(res => {
            if (res.data.error_code === 0) {
                const homeworkList = res.data.data.data;
                // 计算status=3（已批改，未阅）的作业数量
                const correctedCount = homeworkList.filter(item => item.status === 3).length;

                if (correctedCount > 0) {
                    if (cid === 0) {
                        this.vm.$store.commit("homework/updateHomework", {
                            globalCorrected: correctedCount
                        });
                    } else {
                        let obj = {}
                        obj[cid] = homeworkList.filter(item => item.status === 3)
                        this.vm.$store.commit("homework/updateCorrected", obj);
                    }
                } else {
                    if (cid === 0) {
                        this.vm.$store.commit("homework/updateHomework", {
                            globalCorrected: 0
                        });
                    } else {
                        let obj = {}
                        obj[cid] = undefined
                        this.vm.$store.commit("homework/updateCorrected", obj);
                    }
                }
            }
        })
    }

    /**
     * 获取未批改作业
     * @param {number} cid 会话ID
     */
    getUncorrectHomework(cid) {
        service.getUncorrectedList({
            gid: cid,
        }).then(res => {
            if (res.data.error_code === 0) {
                const map = res.data.data;
                if (Object.keys(map).length !== 0) {
                    if (cid === 0) {
                        this.vm.$store.commit("homework/updateHomework", {
                            globalUnCorrect: map
                        });
                    } else {
                        let obj = {}
                        obj[cid] = map
                        this.vm.$store.commit("homework/updateUncorrected", obj);
                    }
                } else {
                    if (cid === 0) {
                        this.vm.$store.commit("homework/updateHomework", {
                            globalUnCorrect: undefined
                        });
                    } else {
                        let obj = {}
                        obj[cid] = undefined
                        this.vm.$store.commit("homework/updateUncorrected", obj);
                    }
                }
            }
        })
    }

    /**
     * 获取未完成作业
     * @param {number} cid 会话ID
     */
    getUnfinishedHomework(cid) {
        service.getIncompleteList({
            gid: cid,
            page: 1,
            pageSize: 1,
        }).then(res => {
            if (res.data.error_code === 0) {
                const homework = res.data.data.data.shift();
                if (homework) {
                    if (cid === 0) {
                        this.vm.$store.commit("homework/updateHomework", {
                            globalUnfinish: 1
                        });
                    } else {
                        let obj = {}
                        obj[cid] = homework
                        this.vm.$store.commit("homework/updateUnfinish", obj);
                    }
                } else {
                    if (cid === 0) {
                        this.vm.$store.commit("homework/updateHomework", {
                            globalUnfinish: 0
                        });
                    } else {
                        let obj = {}
                        obj[cid] = undefined
                        this.vm.$store.commit("homework/updateUnfinish", obj);
                    }
                }
            }
        })
    }
}

export default HomeworkEventHandler
