/**
 * 事件监听管理器
 * 将initMainScreenControllerEvent中的所有监听逻辑抽离出来，按功能模块组织监听器
 */

/**
 * 事件处理器基类
 * 提供通用的属性和方法供子类继承
 */
class BaseEventHandler {
    constructor(vueInstance, autoLoginManager, mainScreenManager) {
        this.vm = vueInstance
        this.autoLoginManager = autoLoginManager
        this.mainScreenManager = mainScreenManager
    }

    /**
     * 初始化事件监听器 - 子类需要实现此方法
     * @param {Object} controller MainScreen控制器
     */
    initEvents(controller) {
        throw new Error('子类必须实现 initEvents 方法')
    }
}
import { resumeAllTasks } from '@/common/oss'
import { parseServerInfo, getLiveRoomObj, closeChatWindowIfNeed, getBaseUrl, findServiceId,
    checkIsCreator, checkIsManager, setIworksInfoToMsg, parseImageListToLocal, patientDesensitization, pushImageToList, setProtocolTree, setViewList } from './common_base'
import Tool from '@/common/tool.js'
import service from '../service/service'
import multiCenterService from '../service/multiCenterService'
import UserEventHandler from './eventHandlers/UserEventHandler'

class EventListenerManager {
    constructor(vueInstance, autoLoginManager, mainScreenManager) {
        this.vm = vueInstance
        this.autoLoginManager = autoLoginManager
        this.mainScreenManager = mainScreenManager
        this.isFirstLoadServerInfo = false
        this.hasSetCurrentList = false
        this.isTopFileTransferAssistant = false
        this.debounceSortChatList = Tool.debounce(this.sortChatList,600,true)

        // 初始化事件处理器
        this.userEventHandler = new UserEventHandler(vueInstance, autoLoginManager, mainScreenManager)
    }

    /**
     * 初始化所有事件监听器
     * @param {Object} controller MainScreen控制器
     */
    initMainScreenControllerEvent(controller) {
        console.log('[EventListenerManager] init controller events')

        this.autoLoginManager.isAutoLogging = false

        // 初始化各类事件监听器
        this._initGatewayEvents(controller)
        this._initMainScreenEvents(controller)
        this.userEventHandler.initEvents(controller)
        this._initConversationEvents(controller)
        this._initNotificationEvents(controller)
        this._initLiveEvents(controller)
        this._initResourceEvents(controller)
        this._initGroupsetEvents(controller)
        this._initHomeworkEvents(controller)
        this._initDeviceEvents(controller)
        this._initMediaTransferEvents(controller)
        this._initServerInfoEvent(controller)

        // 初始化作业相关数据
        this._initHomeworkData()

        // 更新全局参数
        this.vm.$store.commit('globalParams/updateGlobalParams', {
            init_main_screen_time: new Date().getTime()
        })
    }

    /**
     * 初始化网关相关事件
     * @param {Object} controller 控制器
     */
    _initGatewayEvents(controller) {

        controller.on('gateway_connect', () => {
            this.autoLoginManager.onSocketConnectSuccess()
            this.mainScreenManager.onSocketConnectSuccess()
            this.getAllTags()

            this.getMultiCenterOptionList()
            this.getDeviceNameById()
            this.getGroupSetList()
            this.getAiAnalyzeTypes()
            this.vm.updateLiveCount()
            controller.emit("get_consultation_image_list", {
                start: 0,
                count: this.vm.systemConfig.consultationImageShowNum
            }, this.setConsultationImageList.bind(this))

            controller.emit('get_all_hospital_name', this.setAllHospital.bind(this))
            controller.emit('get_user_info')
            controller.emit('get_version_info')
            controller.emit("init_main_screen_controller_event", controller)

            setTimeout(() => {
                resumeAllTasks()
            }, 2000)

            if (this.vm.user.pacsCid) {
                this.vm.joinAndStartConversation(this.vm.user.pacsCid)
            }

            this.vm.$refs['user_avatar'].ifCreateUserAvatar()
            this.vm.$refs['init_organization'].init()
        })

        controller.on("gateway_error", (data) => {
            this.vm.resetApp()
            this.autoLoginManager.onSocketError(data)
        })

        controller.on("gateway_reconnecting", () => {
            this.mainScreenManager.onSocketReconnecting()
        })

        controller.on("gateway_reconnect_fail", (data) => {
            this.autoLoginManager.onSocketReconnectFail()
            this.mainScreenManager.onSocketReconnectFail(data)
        })

        controller.on("gateway_reconnect", () => {
            this.mainScreenManager.onSocketReconnect()
        })

        controller.on("gateway_disconnect", (data) => {
            this.autoLoginManager.onSocketDisconnect(data)
            this.mainScreenManager.onSocketDisconnect(data)
        })
    }

    /**
     * 初始化MainScreen相关事件
     * @param {Object} controller 控制器
     */
    _initMainScreenEvents(controller) {

        controller.on("recent_active_conversation_list", (is_succ, data) => {
            this.setCurrentList(is_succ, data)
            window.CWorkstationCommunicationMng.QueryStandaloneWorkstationShareExamInfo()
        })

        controller.on("recent_active_conversation_list_last_message", (is_succ, data) => {
            this.setLastMessage(is_succ, data)
            this.vm.$store.commit('chatList/updateUnReadMap', data)
        })

        controller.on("friend_list", (is_succ, data) => {
            this.setFriendList(is_succ, data)
            setTimeout(() => {
                this.autoUploadDrAiData()
            }, 2500)
        })

        controller.on("userAddLoginClient", (data) => {
            if (data.allClientType.length > 1) {
                this.isTopFileTransferAssistant = true
                if (this.hasSetCurrentList) {
                    this.topFileTransferAssistant()
                }
            }
        })

        controller.on("conversation_list", (is_succ, data) => {
            this.setGroupList(is_succ, data)
        })

        controller.on("group_applys", (is_succ, data) => {
            this.setGroupApplys(is_succ, data)
        })

        controller.on("friend_applys", (is_succ, data) => {
            this.dealFriendApplys(is_succ, data)
        })
    }



    /**
     * 初始化对话相关事件
     * @param {Object} controller 控制器
     */
    _initConversationEvents(controller) {

        controller.on("notify_start_conversation", (is_succ, conversation, start_type, kickout_data) => {
            this.NotifyStartConversation(is_succ, conversation, start_type, kickout_data)
        })

        controller.on('request_conversation_start_ultrasound_desktop', (data) => {
            this.vm.notifyStartConversationByMonitorWall(data)
        })

        controller.on("receive_group_message", (data) => {
            console.log('receive_group_message', data, 2)
            if (!this.vm.conversationList.hasOwnProperty(data.group_id)) {
                this.setSayChatMessageReceiveGroupMessage(data, false)
                if (data.msg_type === this.vm.systemConfig.msg_type.LIVE_INVITE ||
                    data.groupInfo.service_type === this.vm.systemConfig.ServiceConfig.type.LiveBroadCast) {
                    this.vm.debounceUpdateLiveCount()
                }
                this.debounceSortChatList()
            }
        })
    }

    /**
     * 初始化通知相关事件
     * @param {Object} controller 控制器
     */
    _initNotificationEvents(controller) {

        controller.on("notify_exception", () => {
            this.autoLoginManager.onNotifyException()
        })

        controller.on("NotifyStandaloneWorkstationShareExamInfo", (data) => {
            this.NotifyStandaloneWorkstationShareExamInfo(data)
        })

        controller.on("notify_download_task", (data) => {
            if ("error" === data.type) {
                this.vm.$message.error(data.errorInfo)
            }
        })

        controller.on("notify_update_groupset_portrait", (err, result) => {
            if (!err) {
                this.notifyUpdateGroupsetAvatar(result)
            }
        })
    }

    /**
     * 初始化直播相关事件
     * @param {Object} controller 控制器
     */
    _initLiveEvents(controller) {
        controller.on("notify_agora_live_start", (data) => {
            this.NotifyAgoraLiveStart(data)
        })

        controller.on("notify_agora_live_stop", (data) => {
            this.NotifyAgoraLiveStop(data)
        })

        controller.on("notify_update_recording", (data) => {
            this.NotifyUpdateLiveRecord(data)
        })

        controller.on("notify_update_announcement", (data) => {
            this.NotifyUpdateAnnouncement(data)
        })
    }

    /**
     * 初始化媒体传输相关事件
     * @param {Object} controller 控制器
     */
    _initMediaTransferEvents(controller) {

        controller.on("notify_update_media_transfer_task", (err, result) => {
            if (!err) {
                this.vm.$store.commit('taskList/updateMediaTransferTasks', result.list)
            }
        })

        controller.on("notify_delete_media_transfer_task", (err, result) => {
            if (!err) {
                this.vm.$store.commit('taskList/deleteMediaTransferTasks', result.list)
            }
        })

        controller.emit("query_media_transfer_tasks", {}, (err, result) => {
            if (!err) {
                this.vm.$store.commit('taskList/initMediaTransferTasks', result.list)
            }
        })
    }

    /**
     * 初始化资源相关事件
     * @param {Object} controller 控制器
     */
    _initResourceEvents(controller) {

        controller.on("notify.group.resource.delete.exam", (data) => {
            console.log(data, 'notify.group.resource.delete.exam')
            this.vm.$store.commit('examList/deleteExamListItem', {
                cid: data.groupID,
                exam_id: data.examID,
            })
            this.vm.$root.eventBus.$emit('deleteExamItem')

            if (Array.isArray(data.deleteResourceIDList)) {
                data.deleteResourceIDList.forEach(resource_id => {
                    this.vm.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                        resource_id,
                        data: {
                            state: 0 // 被删除
                        }
                    })
                    this.vm.$store.commit('chatList/updateLastChatMessageByResourceDelete', {
                        cid: data.groupID,
                        data: {
                            msg_type: this.vm.systemConfig.msg_type.ResourceDelete,
                            resource_id
                        }
                    })
                })
            }

            if (Array.isArray(data.deleteMessageIDList)) {
                this.vm.$store.commit("conversationList/deleteChatMessagesByGmsgIdList", {
                    gmsg_id_list: data.deleteMessageIDList,
                    cid: data.groupID
                })
                this.vm.$root.eventBus.$emit('notifyDeleteChatMessages', {
                    cid: data.groupID,
                    gmsg_id_list: data.deleteMessageIDList
                })
            }
        })

        controller.on('notify.group.resource.delete.resource', (data) => {
            if (Array.isArray(data.deleteResourceIDList)) {
                data.deleteResourceIDList.forEach(resource_id => {
                    this.vm.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                        resource_id,
                        data: {
                            state: 0 // 被删除
                        }
                    })
                    if (data.deleteMessageIDList.length > 0) {
                        this.vm.$store.commit('chatList/updateLastChatMessageByResourceDelete', {
                            cid: data.groupID,
                            data: {
                                msg_type: this.vm.systemConfig.msg_type.ResourceDelete,
                                resource_id
                            }
                        })
                    }
                    this.vm.$root.eventBus.$emit('deleteFileToExamList', {
                        cid: data.groupID,
                        resource_id
                    })
                })
            }

            if (Array.isArray(data.deleteMessageIDList)) {
                this.vm.$store.commit("conversationList/deleteChatMessagesByGmsgIdList", {
                    gmsg_id_list: data.deleteMessageIDList,
                    cid: data.groupID
                })
                this.vm.$root.eventBus.$emit('notifyDeleteChatMessages', {
                    cid: data.groupID,
                    gmsg_id_list: data.deleteMessageIDList
                })
            }
        })
    }

    /**
     * 初始化群组相关事件
     * @param {Object} controller 控制器
     */
    _initGroupsetEvents(controller) {

        controller.on("notify_delete_group", (json_str) => {
            let data = typeof json_str === 'string' ? JSON.parse(json_str) : json_str
            this.vm.$store.commit('chatList/deleteChatList', { cid: data.cid })
            this.vm.$store.commit('groupList/deleteGroupList', { cid: data.cid })
            this.vm.$store.commit('conversationList/deleteConversationList', { cid: data.cid })
            this.vm.$store.commit('notifications/deleteGroupApplyByCid', { cid: data.cid })
            this.vm.$store.commit('consultationImageList/deleteConsultationImageListByGroupID', { cid: data.cid })

            window.main_screen.conversation_list[data.cid].onResponseDeleteAttendee()
            let liveRoom = getLiveRoomObj(this.vm.$root.currentLiveCid)
            if (liveRoom) {
                liveRoom.LeaveChannelAux()
                window.CWorkstationCommunicationMng.StopConference()
            }
            closeChatWindowIfNeed(data.cid)
            setTimeout(() => {
                this.vm.$message.success(this.vm.lang.user_exit_group_succ)
            }, 100)
        })

        controller.on("open_register_scan_room_view", (data) => {
            const attributes = {
                mac_addr: data.mac_addr,
                name: "",
                hospital_id: 0,
                ultrasync_box_mac_addr: data.mac_addr
            }
            this.vm.$store.commit('user/updateUser', {
                scan_room: attributes
            })
        })

        controller.on('notify_refresh_manager_groupset_list', (data) => {
            console.log('notify_refresh_manager_groupset_list', data)
            if (data.action === 'delete' && (Number(this.vm.$route.params.groupset_id) === data.groupSetID)) {
                this.vm.$router.replace('/main/index/chat_window/0')
            }
            this.getManagerGroupsetList()
        })

        controller.on('notify_refresh_my_groupset_list', (data) => {
            console.log('notify_refresh_my_groupset_list', data)
            if (data.action === 'delete' && (Number(this.vm.$route.params.groupset_id) === data.groupSetID)) {
                this.vm.$router.replace('/main/index/chat_window/0')
            }
            this.getGroupSetList()
        })
    }

    /**
     * 初始化作业相关事件
     * @param {Object} controller 控制器
     */
    _initHomeworkEvents(controller) {

        controller.on('student_answer_sheet_update', (data) => {
            if (data.type === 'add') {
                if (this.vm.$store.state.homework.globalUnfinish === 0) {
                    this.getUnfinishedHomework(0)
                }
            } else {
                this.getUnfinishedHomework(0)
            }

            data.gidList.forEach(cid => {
                if (this.vm.$store.state.conversationList[cid]) {
                    this.getUnfinishedHomework(cid)
                }
            })

            this.getCorrectedHomework(0)
            data.gidList.forEach(cid => {
                if (this.vm.$store.state.conversationList[cid]) {
                    this.getCorrectedHomework(cid)
                }
            })
        })

        controller.on('teacher_answer_sheet_update', (data) => {
            if (data.type === 'add') {
                this.getUncorrectHomework(0)
            } else {
                this.getUncorrectHomework(0)
            }

            data.gidList.forEach(cid => {
                if (this.vm.$store.state.conversationList[cid]) {
                    this.getUncorrectHomework(cid)
                }
            })
        })

        controller.on('update_ai_analyze_report', () => {
            // console.error('update_ai_analyze_report')
        })
    }

    /**
     * 初始化设备相关事件
     * @param {Object} controller 控制器
     */
    _initDeviceEvents(controller) {

        controller.on('equipment_server_device_alram_update', (data) => {
            let deviceFailure = this.vm.$store.state.device.deviceFailure
            let num = deviceFailure[data.device_id] || 0
            if (data.status === 'NEW') {
                num++
            } else if (data.status === 'RESOLVE') {
                num > 0 ? num-- : 0
            }
            deviceFailure[data.device_id] = num
            this.vm.$store.commit('device/updateDeviceFailure', deviceFailure)
        })
    }

    /**
     * 初始化服务器信息事件
     * @param {Object} controller 控制器
     */
    _initServerInfoEvent(controller) {

        controller.on("server_info", (data) => {
            let json = parseServerInfo(data)
            this.vm.$store.commit('systemConfig/updateSystemConfig', {
                serverInfo: json
            })

            if (json.network_environment === 1 && json.storageReplaceInfo.replace) {
                this.observeImageLoad(json)
            }

            window.CWorkstationCommunicationMng.initServerConfig(json)
            this.initUserConfig2App()

            if (!this.isFirstLoadServerInfo) {
                this.isFirstLoadServerInfo = true
                this.vm.sendSyncAccountOrLiveToULinker()
                this.checkAutoEnterTvWall()

                if (Tool.checkAppClient('Cef')) {
                    this.queryIStationInfo_DR()
                    this.setWhiteBoardUrl()
                    Tool.initNativeAgoraSdk(json.agora_appid).then(async () => {
                        setTimeout(() => {
                            this.ifNeedAutoPushStream()
                        }, 1500)
                    })
                }
            }

            setTimeout(() => {
                this.checkAndSetPrivacyAgreement()
            }, 100)
        })
    }

    /**
     * 初始化作业相关数据
     */
    _initHomeworkData() {
        this.getUnfinishedHomework(0)
        this.getUncorrectHomework(0)
        this.getCorrectedHomework(0)
    }
    getAllTags(){
        service.getAllTags().then((res)=>{
            if (res.data.error_code==0) {
                this.vm.$store.commit('gallery/addTagTopInfo', res.data.data)
            }
        })
    }
    getMultiCenterOptionList(){
        multiCenterService.getMultiCenterAllOptions().then((res)=>{
            if (res.data.error_code==0) {
                this.vm.$store.commit('multicenter/updateMCOptionList',res.data.data);
            }
        })
    }
    getGroupSetList(){
        window.main_screen.getGroupsetList({},(data)=>{
            console.log('getGroupsetList',data)
            if (data.error_code==0) {
                let list=data.data;
                list.forEach(item=>{
                    item.type=this.vm.systemConfig.ConversationConfig.type.GroupSet;
                })
                this.vm.$store.commit('groupset/initGroupsetList',data.data);
            }
        })
        this.getManagerGroupsetList()
    }
    getManagerGroupsetList(){
        window.main_screen.getManagerGroupsetList({},(data)=>{
            console.log(data,'getManagerGroupsetList')
            if (data.error_code==0) {
                let list=data.data;
                list.forEach(item=>{
                    item.type=this.vm.systemConfig.ConversationConfig.type.GroupSet;
                })
                this.vm.$store.commit('groupset/initGroupsetManagerList',data.data);
            }
        })
    }
    getAiAnalyzeTypes(){
        service.getAiAnalyzeTypes().then((res)=>{
            if (res.data.error_code==0) {
                this.vm.$store.commit('aiPresetData/updateAiPresetData',res.data.data)
            }
        })
    }
    queryIStationInfo_DR(){
        Tool.createCWorkstationCommunicationMng({
            name:'queryIStationInfo_DR',
            emitName:'IStationInfo_DR',
            params:{},
            timeout:5000,
        }).then((data)=>{
            window.enable_istation=data.Show;
            this.vm.$store.commit('globalParams/updateGlobalIstationInfo',data)
            this.vm.$store.commit('device/updateDeviceInfo', {
                isIStationInfoDR: true,
            })
        })
        window.CWorkstationCommunicationMng.RequestDrConnectStatus()
    }
    setWhiteBoardUrl(){
        if(Tool.checkAppClient('Cef')){
            let language = window.localStorage.getItem('lang')
            window.CWorkstationCommunicationMng.SetWhiteBoardUrl({
                url:window.location.href.includes('localhost')?
                    `http://localhost:8888/whiteboard.html#/index?language=${language}`
                    :Tool.transferLocationToCe(`${getBaseUrl()}/whiteboard/whiteboard.html#/index?language=${language}`)
            })
        }
    }
    checkAutoEnterTvWall(){
        if(this.vm.user.preferences.auto_enter_tv_wall){
            if(this.vm.functionsStatus.tvwall&&this.vm.user.role>1&&!this.vm.isWorkStation){
                if(location.href.includes('localhost')){
                    return
                }
                this.vm.$root.eventBus.$emit('enterTVmode')
            }

        }
    }
    NotifyUpdateLiveRecord(data){
        console.log('NotifyUpdateLiveRecord',data)
        this.vm.$store.commit("conversationList/updateChatMessageLiveRecordData", {
            group_id: data.gid,
            resource_id: data.resource_id,
            live_record_data: data.resource.more_details,
            coverUrl:data.coverUrl
        });
        this.vm.$store.commit("consultationImageList/updateConsultationLiveRecordData", {
            group_id: data.gid,
            resource_id: data.resource_id,
            live_record_data: data.resource.more_details,
            coverUrl:data.coverUrl
        });
    }
    NotifyStandaloneWorkstationShareExamInfo(data){
        //判断是否在电视墙下，发起实时下，阅片下
        if(window.vm.$store.state.dynamicGlobalParams.tv_wall_mode){//在电视墙下禁止操作
            window.CWorkstationCommunicationMng.ShowConfirmDialog({
                title: this.vm.lang.warning_title,
                message: this.vm.lang.tv_wall_warning_message,
                yes_button: this.vm.lang.confirm_txt,
            });
            window.CWorkstationCommunicationMng.ClearStandaloneWorkstationShareExamInfo();
            return;
        }
        // if(window.vm.$store.state.realtimeVideo.desktop_started){//发起实时下
        //     window.CWorkstationCommunicationMng.ShowConfirmDialog({
        //         title: this.vm.lang.warning_title,
        //         message: this.vm.lang.real_time_warning_message,
        //         yes_button: this.vm.lang.confirm_txt,
        //     });
        //     window.CWorkstationCommunicationMng.ClearStandaloneWorkstationShareExamInfo();
        //     return;
        // }

        if(this.vm.$route.name=='gallery'){//在阅片下，转发窗口盖在最上层
            console.log("gallery");
            this.vm.$root.eventBus.$emit('hideRealTimeVideo');
        }

        console.log("NotifyStandaloneWorkstationShareExamInfo ", data);

        this.vm.$root.eventBus.$emit('openTransmit',{
            callback:this.generalTransmitSubmit,
            isStandaloneWorkstationShare:1,
            cid:this.vm.$route.params.cid,
        })
    }
    notifyUpdateGroupsetAvatar(result){
        let groupset={
            avatar:result.avatar
        }
        this.vm.$store.commit('groupset/updateGroupsetAvatar',{
            avatar:groupset.avatar,
            id:result.groupset_id,
        })
        this.vm.$message.success(this.vm.lang.modify_photo_success);
        this.vm.back();
    }
    setLastMessage(is_succ,list){
        //设置最后一条消息记录
        if (is_succ) {
            for(let item of list){
                item.message.original_msg_body = item.message.msg_body
                item.message.msg_body=this.vm.parseMessageBody(item.message.msg_body)
                if(item.message.been_withdrawn === 2){
                    item.message.msg_type = this.vm.systemConfig.msg_type.WITHDRAW
                }else if(item.message.been_withdrawn === 1){
                    item.message.msg_type = this.vm.systemConfig.msg_type.ResourceDelete
                }
                this.vm.$store.commit('chatList/setLastMessage',item);
            }
        }else{
            this.vm.$message.error('setLastMessage error')
        }
    }

    /**
     * 设置当前聊天列表
     * @param {boolean} is_succ 是否成功
     * @param {Array} list 聊天列表数据
     */
    setCurrentList(is_succ, list) {
        if (is_succ) {
            this.vm.$store.commit('chatList/initChatList', list)
            this.vm.$store.commit('loadingConfig/updateLoaded', {
                key: 'loadedChatList',
                loaded: true
            })

            // 如果多端登录，置顶文件传输助手
            if (this.isTopFileTransferAssistant) {
                this.topFileTransferAssistant()
            }
            this.hasSetCurrentList = true
        } else {
            this.vm.$message.error('setCurrentList error')
        }
    }

    /**
     * 置顶文件传输助手
     */
    async topFileTransferAssistant() {
        // 置顶文件传输助手
        let service_type = this.vm.systemConfig.ServiceConfig.type.FileTransferAssistant
        let analyze = await findServiceId(service_type)
        console.log("文件传输助手id:", analyze)

        if (analyze.cid) {
            // 会话列表中存在文件传输助手
            this.vm.$store.commit('chatList/setTopChat', analyze.cid)
        } else {
            // 会话列表没有则新开一个会话
            const that = this.vm
            let fid = analyze.id
            this.vm.$root.socket.emit("request_start_single_chat_conversation", {
                list: [fid, this.vm.user.uid],
                start_type: undefined,
                mode: this.vm.systemConfig.ConversationConfig.mode.Single,
                type: this.vm.systemConfig.ConversationConfig.type.Single
            }, function(is_succ, data) {
                if (is_succ) {
                    that.$store.commit('conversationList/initConversation', data)
                    that.$store.commit('examList/initExamObj', data)
                    setTimeout(() => {
                        that.$store.commit('chatList/setTopChat', data)
                    }, 1000) // 如果立即执行 setTopChat查不到该cid(data)的会话对象
                } else {
                    that.$message.error(that.lang.start_conversation_error)
                }
            })
        }
    }

    /**
     * 通知开始会话
     * @param {boolean} is_succ 是否成功
     * @param {Object} conversation 会话对象
     * @param {Object} start_type 启动类型
     * @param {Object} kickout_data 踢出数据
     */
    NotifyStartConversation(is_succ, conversation, start_type, kickout_data) {
        window.Logger.save({
            message: 'NotifyStartConversation',
            eventType: `socket_event`,
            data: { conversation, is_succ, start_type, kickout_data },
        });

        if (is_succ) {
            // 后台通知开启会话
            let cid = conversation.id
            let existConversation = this.checkExitConversation(cid)
            if (existConversation) {
                return
            }
            let existChatListItem = this.checkExistChatListItem(cid)
            if (!existChatListItem) {
                // 不存在最近会话列表则加入
                var chatItem = {
                    cid: cid,
                    fid: conversation.fid,
                    is_single_chat: conversation.is_single_chat,
                    subject: conversation.subject,
                    type: conversation.type,
                    sex: conversation.sex,
                    state: conversation.state,
                    avatar: conversation.avatar || '',
                    message: {},
                    service_type: conversation.service_type || 0,
                    user_status: conversation.user_status
                }
                this.vm.$store.commit('chatList/addChatList', chatItem)
            }
            conversation.start_type = start_type;
            conversation.socket = conversation.controller;
            delete conversation.controller;
            this.vm.conversationManager.initConversationControllerEvent(conversation.socket, existChatListItem, () => {
                this.vm.$store.commit('conversationList/setConversation', conversation);
                this.vm.$store.commit('examList/initExamObj', cid);
                if ((0 == conversation.is_single_chat) && (conversation.service_type === 0)) {
                    let groupTemp = {
                        id: conversation.id,
                        subject: conversation.subject,
                        is_single_chat: 0
                    };
                    this.vm.setDefaultImg([groupTemp]);
                    this.vm.$store.commit('groupList/addGroup', groupTemp);
                }

                // this.vm.$root.eventBus.$emit('scrollChatWindow');
                if (this.vm.user.uid != conversation.creator_id) {
                    // 非会话创建者，获取本地viewmode设置
                    let settingsObj = JSON.parse(window.localStorage.getItem(`user_${this.vm.user.uid}_viewmode`) || "{}");
                    let viewmode = settingsObj[`group_${cid}`];
                    if (viewmode != undefined) {
                        this.vm.$store.commit('conversationList/updateViewMode', {
                            cid: conversation.id,
                            value: viewmode
                        });
                    }
                }
                this.vm.$root.eventBus.$emit('setPageType', { cid: cid });
                this.vm.$root.eventBus.$emit('refreshConversationSuccessToChatWindow', cid)
                this.vm.$root.eventBus.$emit('refreshConversationSuccessToConference', cid)
                if (this.vm.$root.transmitQueue['f-' + conversation.fid]) {
                    this.vm.$root.transmitQueue[cid] = this.vm.$root.transmitQueue['f-' + conversation.fid]
                    delete this.vm.$root.transmitQueue['f-' + conversation.fid]
                }
                if (this.vm.$root.transmitQueue_StandaloneWorkstation['f-' + conversation.fid]) {
                    this.vm.$root.transmitQueue_StandaloneWorkstation[cid] = this.vm.$root.transmitQueue_StandaloneWorkstation['f-' + conversation.fid]
                    delete this.vm.$root.transmitQueue_StandaloneWorkstation['f-' + conversation.fid]
                }
                if (this.vm.$root.transmitQueue_StandaloneWorkstation[cid]) {
                    // 开启会话后有待发送的独立工作站转发消息
                    window.CWorkstationCommunicationMng.SendStandaloneWorkstationShareExamInfo(cid);
                }
                if (this.vm.$root.transmitQueue[cid]) {
                    // 开启会话后有待发送的转发消息
                    this.vm.$root.eventBus.$emit('sendTransmitMessage', cid)
                }
                if (this.vm.$root.auto_upload_temp[cid]) {
                    // 开启会话后有DR自动转发的图片
                    this.vm.autoUploadDR(cid)
                }
                if (this.vm.$root.auto_upload_temp['DR_AI']) {
                    // 开启会话后有DR_AIZ助手自动转发的图片
                    this.vm.autoUploadDR(cid, 'DR_AI')
                }
                if (this.vm.$root.droc_auto_upload_temp[cid]) {
                    // 开启会话后有DROC自动转发的文件
                    this.vm.autoUploadDROC(cid)
                }

                if (this.vm.$root.deleteQueue[cid]) {
                    // 开启会话后有待删除的聊天消息
                    this.vm.tryToDeleteMessages(cid, this.vm.$root.deleteQueue[cid])
                }
                if (conversation.is_single_chat == 0 && conversation.avatar === '') { // 判断是否有必要更新群头像
                    if (this.vm.$root.updateGroupAvatarUserList[cid]) {
                        this.vm.$root.updateGroupAvatarUserListcid = undefined;
                    }
                    this.vm.$root.eventBus.$emit('createGroupAvatar', { conversation })
                } else if (this.vm.$root.updateGroupAvatarUserList[cid]) {
                    let item = this.vm.$root.updateGroupAvatarUserList[cid]
                    if (item.id == conversation.id) {
                        this.vm.$root.eventBus.$emit('createGroupAvatar', { conversation })
                        this.vm.$root.updateGroupAvatarUserList[cid] = undefined
                    }
                }
                if (start_type.type === this.vm.systemConfig.start_type.KickoutAttendee && start_type.kickout_data) {
                    this.kickoutAttendeeHandle(start_type.kickout_data, conversation.socket)
                }
                // 获取聊天是否为多中心
                this.findMulticenter(conversation.id)
                this.vm.conversationManager.getConverSationGalleryData(conversation.socket, existChatListItem)
                this.getUnfinishedHomework(conversation.id)
                this.getUncorrectHomework(conversation.id);
                // 获取入群申请数量
                const isCreator = checkIsCreator(conversation.id)
                const isManager = checkIsManager(conversation.id)
                if (isCreator || isManager) {
                    this.getApplyCount(conversation.id)
                }
            });

        } else {
            this.vm.$root.eventBus.$emit('createConversationFail')
        }
    }

    /**
     * 检查会话是否已存在
     * @param {string} cid 会话ID
     * @returns {boolean} 是否存在
     */
    checkExitConversation(cid) {
        if (
            window.main_screen.conversation_list.hasOwnProperty(cid) &&
            window.main_screen.conversation_list[cid].gateway.check &&
            window.vm.$store.state.conversationList[cid].socket
        ) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 踢出参与者处理
     * @param {Object} data 踢出数据
     * @param {Object} controller 控制器
     */
    kickoutAttendeeHandle(data, controller) {
        // 收到某人退群的消息
        const that = this;
        if (data.uid == that.vm.user.id) {
            if (window.vm.$root.currentLiveCid && window.main_screen.conversation_list[window.vm.$root.currentLiveCid]) {
                let liveRoom = getLiveRoomObj(window.vm.$root.currentLiveCid)
                if (!liveRoom) {
                    return
                }
                liveRoom.LeaveChannelAux()
                window.CWorkstationCommunicationMng.StopConference()
                setTimeout(() => {
                    closeChatWindowIfNeed(data.cid);
                }, 300)
            }
        }

        let deleteGroupConnent = function(data) {
            that.vm.$store.commit('chatList/deleteChatList', { cid: data.cid })
            that.vm.$store.commit('groupList/deleteGroupList', { cid: data.cid })
            that.vm.$store.commit('conversationList/deleteConversationList', { cid: data.cid })
            that.vm.$store.commit('notifications/deleteGroupApplyByCid', { cid: data.cid })
            // 删除群相关的图像
            that.vm.$store.commit('consultationImageList/deleteConsultationImageListByGroupID', { cid: data.cid });
            closeChatWindowIfNeed(data.cid);
        }

        if (data.uid == that.vm.user.id) { // 自己退群
            let message = "";
            if (data.initiator_uid == that.vm.user.id) { // 自己主动退群
                message = that.vm.lang.user_exit_group_succ;
                let conversation = that.vm.conversationList[data.cid]
                if (!conversation) {
                    return
                }
                if (conversation.attendeeList && conversation.attendeeList[`attendee_${that.vm.user.id}`]) {
                    conversation.attendeeList[`attendee_${that.vm.user.id}`].attendeeState = 0
                }
                that.vm.$root.eventBus.$emit('createGroupAvatar', {
                    conversation: conversation, callback: () => {
                        controller.emit("response_delete_attendee", function(is_succ) {});
                    }
                })
                deleteGroupConnent(data);
            } else { // 自己被动退群
                message = data.initiator_nickname + " " + that.vm.lang.user_passive_exit_group + " " + data.subject;
                // 给服务器发送消息response_delete_attendee，通知断开参与者
                controller.emit("response_delete_attendee", function(is_succ) {});
                deleteGroupConnent(data);
            }
            that.vm.$message.success(message);
        } else { // 他人退群
            that.vm.$store.commit('conversationList/deleteAttendee', data)
            that.vm.$store.commit('notifications/deleteGroupApplyByUidAndCid', { uid: data.uid, cid: data.cid });
        }
    }

    /**
     * 处理接收到的群消息
     * @param {Object} data 消息数据
     * @param {boolean} is_open_conversation 是否打开会话
     */
    setSayChatMessageReceiveGroupMessage(data, is_open_conversation) {
        console.log(data, 'setSayChatMessageReceiveGroupMessage')
        if (is_open_conversation) {
            const res = this.PreHandleChatMessageByGroupMessage(data)
            if (window.vm.$store.state.conversationList[data.group_id].is_loaded_history_list) {
                this.vm.$store.commit('conversationList/setChatMessage', res)
            }
        } else {
            this.PreHandleChatMessageByGroupMessage(data)
        }
    }
    checkExistChatListItem(cid){
        let exit=false
        const chatList = this.vm.$store.state.chatList.list || []
        for(let chat of chatList){
            if(chat.cid==cid){
                exit=true;
                break;
            }
        }
        return exit;
    }
    /**
     * 判断是否需要添加到聊天列表
     * @param {Object} omessage 原始消息对象
     * @param {boolean} isTop 是否置顶，默认为true
     * @returns {Object} 处理后的消息对象
     */
    judgeIfNeedAddChatList(omessage, isTop = true) {
        if (!omessage) {
            return
        }
        let cid = omessage.group_id || omessage.groupInfo.id
        if (this.vm.conversationList[cid] && !this.vm.conversationList[cid].preferences.is_mute) {
            this.vm.conversationManager.messageNotify('new_message')
            this.vm.notifying = true
        }

        let is_exist_chat_List = this.checkExistChatListItem(cid)
        let message = {
            ...omessage,
            downloading: false,
            gmsg_id: omessage.gmsg_id,
            group_id: omessage.group_id,
            msg_body: "",
            msg_type: omessage.msg_type,
            sendFail: false,
            send_ts: omessage.send_ts,
            sender_id: omessage.sender_id,
            sending: false,
            liveInfo: omessage.liveInfo,
            groupInfo: omessage.groupInfo,
            ai_result: omessage.ai_result,
            avatar: omessage.senderInfo && omessage.senderInfo.avatar,
            nickname: omessage.senderInfo && omessage.senderInfo.nickname,
            sex: omessage.senderInfo && omessage.senderInfo.sex,
            fid: omessage.senderInfo && omessage.senderInfo.id
        }
        if (omessage.groupInfo.type === 2) { // 群聊
            message = { ...omessage.groupInfo, ...message }
        }

        parseImageListToLocal([message], 'url')
        patientDesensitization([message]);
        if (!is_exist_chat_List) {
            //不存在最近会话列表则加入
            let is_single_chat = omessage.groupInfo.type == 1 ? 1 : 0
            var chatItem = {
                cid: cid,
                is_single_chat,
                subject: message.groupInfo.subject,
                type: omessage.groupInfo.type,
                state: 1,
                avatar: is_single_chat ? message.avatar : message.groupInfo.avatar,
                message: {},
                service_type: message.groupInfo.service_type || 0,
                nickname: message.nickname || ''
            }
            if (is_single_chat) {
                chatItem.fid = message.fid
                chatItem.sex = message.sex
            }
            if (isTop) {
                this.vm.$store.commit('chatList/addAndTopChat', chatItem)
            } else {
                this.vm.$store.commit('chatList/addChatList', chatItem)
            }
        } else {
            if (isTop) {
                this.vm.$store.commit('chatList/setTopChat', cid)
            }
        }
        return message
    }

    /**
     * 预处理群消息
     * @param {Object} omessage 原始消息对象
     * @returns {Object} 处理后的消息对象
     */
    PreHandleChatMessageByGroupMessage(omessage) {
        console.log('PreHandleChatMessageByGroupMessage', omessage)
        if (!omessage) {
            return
        }
        let message = this.judgeIfNeedAddChatList(omessage)
        let cid = message.group_id
        message.original_msg_body = message.msg_body
        message.msg_body = this.vm.parseMessageBody(message.msg_body)

        if (message.msg_type == this.vm.systemConfig.msg_type.AI_ANALYZE) {
            if (message.ai_analyze && message.ai_analyze.messages) {
                parseImageListToLocal(message.ai_analyze.messages, 'url')
                let ignoreConsultationImages = false
                if (this.vm.conversationList[cid] && (this.vm.systemConfig.msg_type.AI_ANALYZE == this.vm.conversationList[cid].service_type)) {
                    //AI分析图片不放入总图像列表里
                    ignoreConsultationImages = true
                }
                for (let item of message.ai_analyze.messages) {
                    let obj = Object.assign({}, item, true)
                    pushImageToList(obj, ignoreConsultationImages)
                }
            }
        }

        if (message.msg_type == this.vm.systemConfig.msg_type.Text) {
            if (Array.isArray(message.mentionList)) {
                if (message.mentionList.includes(this.vm.user.uid) || message.mentionList.includes('all')) {
                    this.vm.$store.commit('chatList/setMention', message)
                    this.vm.conversationManager.messageNotify('new_message')
                    this.vm.notifying = true
                }
            }
        }

        if (message.protocol_guid) {
            //消息存在iworks信息
            if (message.iworks_protocol) {
                this.vm.setIworksProtocol(message.iworks_protocol)
            }
            if (message.iworks_protocol_execution) {
                this.vm.setIworksProtocol(message.iworks_protocol_execution)
            }
            setIworksInfoToMsg(message);
        }

        this.vm.$store.commit('chatList/addMessageNoSort', message)
        if (!this.isResource(message.msg_type)) {
            this.vm.$store.commit('chatList/addUnread', {
                group_id: message.group_id
            })
            if (Tool.checkAppClient('Cef')) {
                let liveRoom = getLiveRoomObj();
                if (liveRoom) {
                    liveRoom.updateUnReadMsgCount()
                }
            }
        }

        pushImageToList(message);
        this.vm.$root.eventBus.$emit('updateExamImageListIfNeed', message)
        this.vm.addChatMessageTask(message, false);
        this.vm.notifying = false

        return {
            list: [message],
            cid: cid,
            type: 'append',
            is_localdb_msg: 0
        }
    }

    // 检查并自动设置隐私协议状态
    checkAndSetPrivacyAgreement() {
        const serverType = localStorage.getItem('serverType') || '云++';
        const privacyStatus = JSON.parse(localStorage.getItem('isAgreePrivacyPolicy') || "{}");
        const privacy_version = this.vm.$store.state.systemConfig.envConfig &&
                                  this.vm.$store.state.systemConfig.envConfig.privacy_agreement_version;

        // 获取用户已同意的版本号
        const agreedVersion = privacyStatus[serverType];

        // 检查是否没有同意记录或记录为空（表示曾经撤销过）
        const hasNoAgreement = !agreedVersion || agreedVersion === '' || agreedVersion === 0;

        if (hasNoAgreement && privacy_version) {
            console.log('检测到用户没有隐私协议同意记录，自动设置为已同意状态');

            // 自动设置为已同意
            privacyStatus[serverType] = privacy_version;
            localStorage.setItem('isAgreePrivacyPolicy', JSON.stringify(privacyStatus));

            // 向客户端通知隐私协议状态
            if (window.CWorkstationCommunicationMng && window.CWorkstationCommunicationMng.setPrivacyPolicyStatus) {
                window.CWorkstationCommunicationMng.setPrivacyPolicyStatus({
                    status: 1,
                    version: privacy_version
                });
            }

            console.log('隐私协议状态已自动设置为已同意，版本号:', privacy_version);
        } else if (!privacy_version) {
            console.log('等待获取隐私协议版本号...');
        } else {
            console.log('用户已有隐私协议同意记录，版本号:', agreedVersion);
        }
    }
    observeImageLoad(){
        const fallbackImageUrl = 'static/resource_pc/images/slt_err.png';
        Tool.observeImageLoad(fallbackImageUrl)
    }
    setConsultationImageList(is_succ,data){
        console.log('setConsultationImageList',is_succ,data)
        //放置图像列表数据
        if(is_succ){
            patientDesensitization(data.consultation_image_list);
            parseImageListToLocal(data.consultation_image_list,'url')
            if(data.iworks_protocol_list){
                this.setGalleryMessageDetail(is_succ,{iworks_protocol_list:data.iworks_protocol_list})
            }
            let consultation_image_list = data.consultation_image_list
            for (let item of  data.consultation_image_list) {
                setIworksInfoToMsg(item);
            }
            if(is_succ != "net_error"){
                this.vm.$store.commit('consultationImageList/initConsultationImages',data)
            }
            this.vm.$store.commit('loadingConfig/updateLoaded',{
                key:'loadedFileList',
                loaded:true
            });
        }else{
            this.vm.$message.error('setConsultationImageList error')
        }
    }
    setFriendList(is_succ,list){
        //设置好友列表
        if (is_succ) {
            this.vm.$store.commit('loadingConfig/updateLoaded',{
                key:'loadedFriendList',
                loaded:true
            });
            this.vm.$store.commit('friendList/initFriendList',list);
            sortFriendList();
        }else{
            this.vm.$message.error('setFriendList error')
        }
    }
    setGroupList(is_succ,data){
        //设置群组列表
        if (is_succ) {
            var list =this.vm.parseObjToArr(data)
            this.vm.$store.commit('loadingConfig/updateLoaded',{
                key:'loadedGroupList',
                loaded:true
            });
            this.vm.$store.commit('groupList/initGroupList',list);
        }else{
            this.vm.$message.error('setGroupList error')
        }
    }
    setGroupApplys(list){
        //设置入群申请
        if (list.length>0) {
            this.vm.setDefaultImg(list);
            for(let item of list){
                this.vm.$store.commit('notifications/addGroupApply',item);
            }
            this.vm.conversationManager.messageNotify('join_group')
        }

    }
    setAllHospital(is_succ,data){
        console.error('setAllHospital',this)
        if (is_succ) {
            this.vm.$store.commit('dynamicGlobalParams/updateDynamicGlobalParams',{
                hospitals:data
            })
        }
    }
    dealFriendApplys(data){
        //设置好友申请
        if(data.notify_type=='request_add_friend'){
            var temp=[]
            temp.push(data.param)
            this.vm.$store.commit('notifications/addFriendApply',data);
            this.vm.conversationManager.messageNotify('friend_apply')
        }else if(data.notify_type=="response_add_friend"){
            if (data.param.accept) {
                var str=this.vm.lang.response_accept_friend.replace('${1}',data.param.nickname)
            }else{
                var str=this.vm.lang.response_disaccept_friend.replace('${1}',data.param.nickname);
                this.vm.$store.commit('relationship/removeApplyingList',data.param)
            }
            this.vm.$message.success(str)
        }
    }

    setGalleryMessageDetail(is_succ,data){
        console.log('setGalleryMessageDetail----------------',JSON.parse(JSON.stringify(data)))
        if (is_succ) {
            //将评论信息、iworks协议放入画廊中
            for(let key in data.iworks_protocol_list){
                let protocol=data.iworks_protocol_list[key];
                protocol.protocolTree=[setProtocolTree(protocol)]
                protocol.viewList=this.vm.setViewList(protocol.protocolTree[0],[])
            }
            this.vm.$store.commit('gallery/setCommentToGallery',data)
        }else{
            this.vm.$message.error('setGalleryMessageDetail error')
        }
    }
    ifNeedAutoPushStream(){
        let odata  = localStorage.getItem('auto_push_stream_' + this.vm.user.id);
        if(odata){
            let data = JSON.parse(odata)
            let isAutoPushStream = data.enable
            let lastPushStreamCid = data.value
            if(isAutoPushStream&&lastPushStreamCid){
                this.requestConversationToStartUltrasoundDesktopByAutoPushStream(data)
            }

        }
        this.vm.$store.commit('liveConference/updateConferenceValue',{
            autoPushReady:true,
        })
    }
    requestConversationToStartUltrasoundDesktopByAutoPushStream(data){
        var auto_push_stream = this.vm.$store.state.globalParams.auto_push_stream;

        if (!this.vm.functionsStatus.live
             ||!auto_push_stream
             || !auto_push_stream.enable
             || !auto_push_stream.value_type
             || !auto_push_stream.value) {
            return;
        }

        if ("Conversation" == auto_push_stream.value_type && 0 < auto_push_stream.value) {
            var cid = auto_push_stream.value;
            this.requestGroupToStartUltrasoundDesktopByAutoPushStream({cid:cid, conversation:data});
        } else if ("FriendId" == auto_push_stream.value_type && 0 < auto_push_stream.value) {
            var fid = auto_push_stream.value;
            this.requestFriendToStartUltrasoundDesktopByAutoPushStream({fid:fid, conversation:data});
        }
    }
    requestGroupToStartUltrasoundDesktopByAutoPushStream(data){
        let cid = 0;
        let conversation = null;
        if (0 == cid) {
            this.vm.openConversation(data.cid,2,null,(is_suc)=>{
                var input_data={
                    gid:data.cid,
                    record_mode:data.conversation.record_mode?1:0
                }
                let newConversation = this.vm.$store.state.conversationList[data.cid]
                newConversation.socket.emit('edit_record_mode',input_data,function(is_succ,data){
                    if(is_succ){
                        //修改成功
                        this.vm.$store.commit('conversationList/updateIsLiveRecord',{
                            cid:input_data.gid,
                            record_mode:input_data.record_mode
                        });
                    }else{//修改失败
                    }
                    console.log('requestGroupToStartUltrasoundDesktopByAutoPushStream:', this.vm.$store.state.conversationList[input_data.gid])
                    this.startUltrasoundDesktopByAutoPushStream();
                })
                // is_suc&&this.startUltrasoundDesktopByAutoPushStream(conversation);
            });
        } else {
            this.vm.openConversation(cid,2,null,()=>{
                this.startUltrasoundDesktopByAutoPushStream();
            });
        }
    }
    requestFriendToStartUltrasoundDesktopByAutoPushStream(data){
        console.log('requestFriendToStartUltrasoundDesktopByAutoPushStream',data)
        let cid = 0;
        if (0 == cid) {
            this.vm.openConversation(data.fid,3,null,(is_suc)=>{
                var input_data={
                    gid:data.cid,
                    record_mode:data.conversation.record_mode?1:0
                }
                this.vm.$root.socket.emit('edit_record_mode',input_data,function(is_succ,data){
                    if(is_succ){
                        //修改成功
                        this.vm.$store.commit('conversationList/updateIsLiveRecord',{
                            cid:input_data.gid,
                            record_mode:input_data.record_mode
                        });
                    }else{//修改失败
                    }
                    this.startUltrasoundDesktopByAutoPushStream();
                })
                // is_suc&&this.startUltrasoundDesktopByAutoPushStream();
            });
        } else {
            this.vm.openConversation(cid,2,null,()=>{
                this.startUltrasoundDesktopByAutoPushStream();
            });
        }

    }
    startUltrasoundDesktopByAutoPushStream(){
        if(!this.vm.$root.currentLiveCid){
            setTimeout(()=>{
                this.vm.$root.eventBus.$emit('chatWindowStartJoinRoom',{main:1,aux:1,videoSource:'doppler',isSender:1,autoPushStream:true});
            },1000)
        }
    }
    autoUploadDrAiData(){
        console.log('autoUploadDrAiData')
        if(this.vm.$root&&this.vm.$root.auto_upload_temp['DR_AI']&&this.vm.$root.auto_upload_temp['DR_AI'].length>0){
            console.log('autoUploadDrAiData',JSON.stringify(this.vm.$root.auto_upload_temp['DR_AI']))
            let json = this.vm.$root.auto_upload_temp['DR_AI'].shift()
            if(json&&json.ImageList){
                this.notifyNewExamImages(json)
            }
        }
    }
    notifyNewExamImages(json){
        if(!(json&&json.ImageList&&json.ImageList.length>0)){
            return
        }
        let receiver = json.Receiver||''
        if(receiver&&this.vm.$store.state.friendList.list.length<1){
            if(receiver=='DR_AI'){
                this.vm.$root.auto_upload_temp[receiver]=this.vm.$root.auto_upload_temp[receiver]||[]
                this.vm.$root.auto_upload_temp[receiver].push(json)
            }
            return
        }
        if(receiver=='DR_AI'||(this.vm.user.preferences&&this.vm.user.preferences.auto_upload==1)){
            //DR_AI 数据
            var fid=0
            if(receiver=='DR_AI'){
                for(let item of this.vm.$store.state.friendList.list){
                    if (item.service_type==this.systemConfig.ServiceConfig.type.DrAiAnalyze) {
                        fid = item.id
                        break
                    }
                }
            }else{
                //设置了自动上传
                var default_conversation=this.vm.user.preferences.default_conversation||{}
                if (default_conversation.type==0) {
                    this.autoUploadToConversation(default_conversation.cid,json)
                    return
                }else if(default_conversation.type==2){
                    fid=default_conversation.fid
                }
            }
            if(fid>0){
                //转发到好友
                const chatList = this.vm.$store.state.chatList.list || []
                for(let item of chatList){
                    if (item.fid==fid) {
                        this.autoUploadAction(item.cid,json,2);
                        return
                    }
                }
                //好友存在于好友列表中
                for(let item of this.vm.$store.state.friendList.list){
                    if (item.id==fid) {
                        this.vm.openConversation(fid,3,{type:this.systemConfig.start_type.SendTo})
                        return
                    }
                }
            }
        }
    }
    autoUploadToConversation(cid,json){
        let tag=true;
        let searchArr=this.vm.$store.state.groupList.concat(this.vm.$store.state.chatList.list)
        for(let item of searchArr){
            if ((item.id||item.cid)==cid) {
                tag=false;
                break;
            }
        }
        if (tag) {
            //群已解散或被踢出
            return ;
        }
        this.autoUploadAction(cid,json);

    }
    autoUploadDR(cid, type=''){
        console.log('autoUploadDROC',cid, type)
        let arr=this.vm.$root.auto_upload_temp[cid]||[];
        if(type&&this.vm.$root.auto_upload_temp[type].length>0){
            arr=[...this.vm.$root.auto_upload_temp[type],...arr];
            delete this.vm.$root.auto_upload_temp[type]
        }
        console.log('autoUploadDROC list',arr)
        for(let item of arr){
            window.CWorkstationCommunicationMng.addExamImages(cid,item.ImageList)
            console.log('autoUploadDR',item,cid)
        }
        this.vm.$root.auto_upload_temp[cid]=null;
    }
    autoUploadDROC(cid){
        console.log('autoUploadDROC',cid)
        let arr=this.vm.$root.droc_auto_upload_temp[cid];
        for(let item of arr){
            window.CWorkstationCommunicationMng.NotifySendFileToConversation(item);
            console.log('autoUploadDROC',item,cid)
        }
        this.vm.$root.droc_auto_upload_temp[cid]=null;
    }
    autoUploadAction(cid,json,open_type=2){
        if (this.vm.$store.state.conversationList[cid]) {
            //会话已开启直接转发
            console.log('autoUploadDR',json,cid)
            window.CWorkstationCommunicationMng.addExamImages(cid,json.ImageList)
        }else{
            //暂存数据，先开启会话
            if(this.vm.$root.auto_upload_temp[cid]){
                this.vm.$root.auto_upload_temp[cid].push(json)
            }else{
                this.vm.$root.auto_upload_temp[cid]=[json]
            }
            this.vm.openConversation(cid,open_type,{type:this.systemConfig.start_type.SendTo})
        }
    }
    generalTransmitSubmit(data){
        console.log("generalTransmitSubmit ", data);
        if(this.vm.$route.name=='gallery'){
            this.vm.$root.eventBus.$emit('showRealTimeVideo');
        }

        Object.entries(this.vm.$store.state.conversationList).map(item=>{ // 判断会话列表中是否有与fid对应的会话 有则直接取之前的cid
            if(data.id &&item[1].fid === data.id){
                data.cid = item[0]
            }
        })
        this.vm.$root.transmitQueue_StandaloneWorkstation[data.cid||'f-'+data.id]=1;
        if (this.vm.$store.state.conversationList[data.cid]) {
            //会话已开启则直接转发
            window.CWorkstationCommunicationMng.SendStandaloneWorkstationShareExamInfo(data.cid);
        }else{
            //会话未开启则开启会话
            if (data.cid) {
                this.vm.openConversation(data.cid,7)
            }else{
                this.vm.openConversation(data.id,3)
            }
        }
    }
    NotifyAgoraLiveStart(data){
        this.vm.$store.commit('liveConference/updateConferenceState',{
            cid:data.groupInfo.id,
            obj:{
                conferenceState:1,
                senderUserId:data.host_uid
            }

        })
        const info = {
            ...data.groupInfo,
            nickname:data.hostNickname,
            fid:data.host_uid,
            avatar:data.hostAvatar||data.groupInfo.avatar
        }
        this.vm.$set(this.vm,'livingGroupInfo',info)
        this.debounceSortChatList()
        if(!data.hasOwnProperty('liveStartTime')){ //该直播没有曾经开播时间。
            this.vm.showLivingNotifyDialog = true
        }

    }
    NotifyAgoraLiveStop(data){
        this.vm.$store.commit('liveConference/updateConferenceState',{
            cid:data.groupInfo.id,
            obj:{
                conferenceState:0,
                senderUserId:0
            }
        })
        this.vm.showLivingNotifyDialog = false
    }
    sortChatList(){
        this.vm.$store.commit('chatList/sortChatList')
    }

    findMulticenter(cid){
        //查询群是否属于多中心
        // let controller = conversation.socket
        const controller=window.main_screen.conversation_list[cid];
        controller&&controller.getMulticenter({},(res)=>{
            if (!res.error_code) {
                let multicenter_list = []
                if(res.data){
                    multicenter_list = res.data.constructor == Array? [...res.data] : [res.data]
                }
                let data={
                    list:multicenter_list,
                    cid: cid,
                }
                this.vm.$store.commit('conversationList/updateMulticenterList',data)
            }
        })
    }
    NotifyUpdateAnnouncement(data){
        if(data.switch){
            this.vm.$store.commit('globalParams/updateGlobalParams',{
                closedNotifyBar:false,
                announcementContent:data.content,
            })
        }else{
            this.vm.$store.commit('globalParams/updateGlobalParams',{
                closedNotifyBar:true,
                announcementContent:'',
            })
        }
    }
    getCorrectedHomework(cid){
        service.getFinishedList({
            gid:cid,
            page:1,
            pageSize:30,
            // 不使用status参数，改为在获取数据后过滤
        }).then(res=>{
            if (res.data.error_code === 0) {
                const homeworkList = res.data.data.data;
                // 计算status=3（已批改，未阅）的作业数量
                const correctedCount = homeworkList.filter(item => item.status === 3).length;


                if (correctedCount > 0) {
                    if (cid === 0) {
                        this.vm.$store.commit("homework/updateHomework", {
                            globalCorrected: correctedCount
                        });
                    } else {
                        let obj = {}
                        obj[cid] = homeworkList.filter(item => item.status === 3)
                        this.vm.$store.commit("homework/updateCorrected", obj);
                    }
                } else {
                    if (cid === 0) {
                        this.vm.$store.commit("homework/updateHomework", {
                            globalCorrected: 0
                        });
                    } else {
                        let obj = {}
                        obj[cid] = undefined
                        this.vm.$store.commit("homework/updateCorrected", obj);
                    }
                }
            }
        })
    }
    getUncorrectHomework(cid){
        service.getUncorrectedList({
            gid:cid,
        }).then(res=>{
            if (res.data.error_code === 0) {
                const map = res.data.data;
                if (Object.keys(map).length !== 0) {
                    if (cid === 0) {
                        this.vm.$store.commit("homework/updateHomework", {
                            globalUnCorrect:map
                        });
                    }else{
                        let obj = {}
                        obj[cid] = map
                        this.vm.$store.commit("homework/updateUncorrected",obj);
                    }
                }else{
                    if (cid === 0) {
                        this.vm.$store.commit("homework/updateHomework", {
                            globalUnCorrect:undefined
                        });
                    }else{
                        let obj = {}
                        obj[cid] = undefined
                        this.vm.$store.commit("homework/updateUncorrected",obj);
                    }
                }
            }
        })
    }
    getUnfinishedHomework(cid){
        service.getIncompleteList({
            gid:cid,
            page:1,
            pageSize:1,
        }).then(res=>{
            if (res.data.error_code === 0) {
                const homework = res.data.data.data.shift();
                if (homework) {
                    if (cid === 0) {
                        this.vm.$store.commit("homework/updateHomework", {
                            globalUnfinish:1
                        });
                    }else{
                        let obj = {}
                        obj[cid] = homework
                        this.vm.$store.commit("homework/updateUnfinish",obj);
                    }
                }else{
                    if (cid === 0) {
                        this.vm.$store.commit("homework/updateHomework", {
                            globalUnfinish:0
                        });
                    }else{
                        let obj = {}
                        obj[cid] = undefined
                        this.vm.$store.commit("homework/updateUnfinish",obj);
                    }
                }
            }
        })
    }
    getApplyCount(cid){
        window.main_screen.conversation_list[cid].getApplyCount({},(res)=>{
            if(res.error_code===0){
                this.vm.$store.commit('conversationList/updateConversation',{
                    cid:cid,
                    key:'applyCount',
                    value:res.data
                });
            }
        })
    }
    getDeviceNameById(){
        if(!this.vm.isCef){
            return
        }
        if (!this.vm.$store.state.deviceInfo.device_id) {
            setTimeout(()=>{
                this.getDeviceNameById();
            },3000)
            return ;
        }
        return new Promise((resolve,reject)=>{
            const params = {
                deviceId:this.vm.$store.state.deviceInfo.device_id,
            }
            window.main_screen.getDeviceNameById(params,(res)=>{
                if(res.error_code === 0){
                    this.vm.$store.commit('device/updateDeviceInfo',{device_name:res.data.name})
                    resolve(true)
                }else{
                    reject(res.error_msg)
                }

            })
        })
    }
    isResource(type){
        if (type==this.vm.systemConfig.msg_type.Image||
        type==this.vm.systemConfig.msg_type.Frame||
        type==this.vm.systemConfig.msg_type.OBAI||
        type==this.vm.systemConfig.msg_type.Cine||
        type==this.vm.systemConfig.msg_type.Video||
        type==this.vm.systemConfig.msg_type.RealTimeVideoReview||
        type==this.vm.systemConfig.msg_type.VIDEO_CLIP
        ) {
            return true
        }else{
            return false;
        }
    }
    initUserConfig2App(){
        var user_config = {
            auto_upload: 0
        };
        if (this.vm.user.preferences && this.vm.user.preferences.auto_upload) {
            user_config.auto_upload = 1;
        }
        console.log('--')
        console.log('upload_config',user_config)
        console.log('--')
        window.CWorkstationCommunicationMng.initUserConfig(user_config);
    }
}

export default EventListenerManager
