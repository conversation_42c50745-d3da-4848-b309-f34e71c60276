import BaseEventHandler from './BaseEventHandler'
import { parseServerInfo, getBaseUrl } from '../common_base'
import Tool from '@/common/tool.js'

/**
 * 服务器信息事件处理器
 * 处理服务器配置相关的事件
 */
class ServerInfoEventHandler extends BaseEventHandler {
    constructor(vueInstance, autoLoginManager, mainScreenManager) {
        super(vueInstance, autoLoginManager, mainScreenManager)
        this.isFirstLoadServerInfo = false
    }

    /**
     * 初始化服务器信息相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on("server_info", (data) => {
            let json = parseServerInfo(data)
            this.vm.$store.commit('systemConfig/updateSystemConfig', {
                serverInfo: json
            })

            if (json.network_environment === 1 && json.storageReplaceInfo.replace) {
                this.observeImageLoad(json)
            }

            window.CWorkstationCommunicationMng.initServerConfig(json)
            this.initUserConfig2App()

            if (!this.isFirstLoadServerInfo) {
                this.isFirstLoadServerInfo = true
                this.vm.sendSyncAccountOrLiveToULinker()
                this.checkAutoEnterTvWall()

                if (Tool.checkAppClient('Cef')) {
                    this.queryIStationInfo_DR()
                    this.setWhiteBoardUrl()
                    Tool.initNativeAgoraSdk(json.agora_appid).then(async () => {
                        setTimeout(() => {
                            this.ifNeedAutoPushStream()
                        }, 1500)
                    })
                }
            }

            setTimeout(() => {
                this.checkAndSetPrivacyAgreement()
            }, 100)
        })
    }

    /**
     * 观察图片加载
     * @param {Object} json 服务器信息
     */
    observeImageLoad(json) {
        const fallbackImageUrl = 'static/resource_pc/images/slt_err.png';
        Tool.observeImageLoad(fallbackImageUrl)
    }

    /**
     * 初始化用户配置到应用
     */
    initUserConfig2App() {
        var user_config = {
            auto_upload: 0
        };
        if (this.vm.user.preferences && this.vm.user.preferences.auto_upload) {
            user_config.auto_upload = 1;
        }
        console.log('--')
        console.log('upload_config', user_config)
        console.log('--')
        window.CWorkstationCommunicationMng.initUserConfig(user_config);
    }

    /**
     * 查询IStation信息
     */
    queryIStationInfo_DR() {
        Tool.createCWorkstationCommunicationMng({
            name: 'queryIStationInfo_DR',
            emitName: 'IStationInfo_DR',
            params: {},
            timeout: 5000,
        }).then((data) => {
            window.enable_istation = data.Show;
            this.vm.$store.commit('globalParams/updateGlobalIstationInfo', data)
            this.vm.$store.commit('device/updateDeviceInfo', {
                isIStationInfoDR: true,
            })
        })
        window.CWorkstationCommunicationMng.RequestDrConnectStatus()
    }

    /**
     * 设置白板URL
     */
    setWhiteBoardUrl() {
        if (Tool.checkAppClient('Cef')) {
            let language = window.localStorage.getItem('lang')
            window.CWorkstationCommunicationMng.SetWhiteBoardUrl({
                url: window.location.href.includes('localhost') ?
                    `http://localhost:8888/whiteboard.html#/index?language=${language}`
                    : Tool.transferLocationToCe(`${getBaseUrl()}/whiteboard/whiteboard.html#/index?language=${language}`)
            })
        }
    }

    /**
     * 检查自动进入电视墙
     */
    checkAutoEnterTvWall() {
        if (this.vm.user.preferences.auto_enter_tv_wall) {
            if (this.vm.functionsStatus.tvwall && this.vm.user.role > 1 && !this.vm.isWorkStation) {
                if (location.href.includes('localhost')) {
                    return
                }
                this.vm.$root.eventBus.$emit('enterTVmode')
            }
        }
    }

    /**
     * 检查是否需要自动推流
     */
    ifNeedAutoPushStream() {
        let odata = localStorage.getItem('auto_push_stream_' + this.vm.user.id);
        if (odata) {
            let data = JSON.parse(odata)
            let isAutoPushStream = data.enable
            let lastPushStreamCid = data.value
            if (isAutoPushStream && lastPushStreamCid) {
                this.requestConversationToStartUltrasoundDesktopByAutoPushStream(data)
            }
        }
        this.vm.$store.commit('liveConference/updateConferenceValue', {
            autoPushReady: true,
        })
    }

    /**
     * 请求会话开始超声桌面自动推流
     * @param {Object} data 数据
     */
    requestConversationToStartUltrasoundDesktopByAutoPushStream(data) {
        // 这个方法需要从原文件中完整移动过来
        // 暂时保留空实现，后续会完善
    }

    /**
     * 检查并自动设置隐私协议状态
     */
    checkAndSetPrivacyAgreement() {
        const serverType = localStorage.getItem('serverType') || '云++';
        const privacyStatus = JSON.parse(localStorage.getItem('isAgreePrivacyPolicy') || "{}");
        const privacy_version = this.vm.$store.state.systemConfig.envConfig &&
            this.vm.$store.state.systemConfig.envConfig.privacy_agreement_version;

        // 获取用户已同意的版本号
        const agreedVersion = privacyStatus[serverType];

        // 检查是否没有同意记录或记录为空（表示曾经撤销过）
        const hasNoAgreement = !agreedVersion || agreedVersion === '' || agreedVersion === 0;

        if (hasNoAgreement && privacy_version) {
            console.log('检测到用户没有隐私协议同意记录，自动设置为已同意状态');

            // 自动设置为已同意
            privacyStatus[serverType] = privacy_version;
            localStorage.setItem('isAgreePrivacyPolicy', JSON.stringify(privacyStatus));

            // 向客户端通知隐私协议状态
            if (window.CWorkstationCommunicationMng && window.CWorkstationCommunicationMng.setPrivacyPolicyStatus) {
                window.CWorkstationCommunicationMng.setPrivacyPolicyStatus({
                    status: 1,
                    version: privacy_version
                });
            }

            console.log('隐私协议状态已自动设置为已同意，版本号:', privacy_version);
        } else if (!privacy_version) {
            console.log('等待获取隐私协议版本号...');
        } else {
            console.log('用户已有隐私协议同意记录，版本号:', agreedVersion);
        }
    }
}

export default ServerInfoEventHandler
