# EventListenerManager 方法迁移状态

## 概述

本文档记录了从原始 `EventListenerManager.js` 文件向新的事件处理器架构迁移方法的状态。

## 已完成的迁移

### ConversationEventHandler.js

#### ✅ 已迁移的方法

1. **setCurrentList(is_succ, list)**
   - 设置当前聊天列表
   - 完整迁移，包括文件传输助手置顶逻辑

2. **setLastMessage(is_succ, list)**
   - 设置最后一条消息记录
   - 完整迁移，包括消息体解析和撤回处理

3. **setFriendList(is_succ, list)**
   - 设置好友列表
   - 完整迁移，包括加载状态更新和排序

4. **setGroupList(is_succ, data)**
   - 设置群组列表
   - 完整迁移，包括数据转换和状态更新

5. **setGroupApplys(list)**
   - 设置入群申请
   - 完整迁移，包括默认图片设置和通知

6. **dealFriendApplys(data)**
   - 处理好友申请
   - 完整迁移，包括申请和响应处理

7. **topFileTransferAssistant()**
   - 置顶文件传输助手
   - 完整迁移，包括异步查找和会话创建

8. **getManagerGroupsetList()**
   - 获取管理员群组列表
   - 完整迁移

9. **getGroupSetList()**
   - 获取群组列表
   - 完整迁移

10. **NotifyStandaloneWorkstationShareExamInfo(data)**
    - 通知独立工作站共享检查信息
    - 完整迁移

11. **notifyUpdateGroupsetAvatar(result)**
    - 通知更新群组头像
    - 完整迁移

12. **generalTransmitSubmit(data)**
    - 通用转发提交
    - 完整迁移

#### 🔄 部分迁移的方法

1. **NotifyStartConversation(is_succ, conversation, start_type, kickout_data)**
   - 通知开始会话
   - ⚠️ 简化实现，省略了复杂的会话初始化逻辑
   - 需要从原文件迁移完整的实现

2. **setSayChatMessageReceiveGroupMessage(data, is_open_conversation)**
   - 处理接收到的群消息
   - ⚠️ 简化实现，依赖PreHandleChatMessageByGroupMessage

3. **PreHandleChatMessageByGroupMessage(omessage)**
   - 预处理群消息
   - ⚠️ 简化实现，缺少AI分析、提及处理等复杂逻辑

#### 🆕 新增的辅助方法

1. **checkExitConversation(cid)**
   - 检查会话是否已存在

2. **checkExistChatListItem(cid)**
   - 检查聊天列表项是否存在

3. **judgeIfNeedAddChatList(omessage, isTop)**
   - 判断是否需要添加到聊天列表

### 其他处理器

#### GatewayEventHandler.js
- ⚠️ 大部分方法为空实现，需要迁移：
  - getAllTags()
  - getMultiCenterOptionList()
  - getDeviceNameById()
  - getGroupSetList()
  - getAiAnalyzeTypes()
  - setConsultationImageList()
  - setAllHospital()

#### UserEventHandler.js
- ⚠️ 所有方法为空实现，需要迁移用户相关的具体逻辑

#### HomeworkEventHandler.js
- ✅ 已完整实现作业相关的方法

#### OtherEventHandler.js
- ✅ 已完整实现媒体传输和设备相关的方法

#### ServerInfoEventHandler.js
- ✅ 大部分方法已实现，少数方法需要完善

## 待迁移的重要方法

### 高优先级

1. **NotifyStartConversation** 的完整实现
   - 包括会话初始化、参与者管理、群头像创建等

2. **PreHandleChatMessageByGroupMessage** 的完整实现
   - 包括AI分析处理、图片列表处理、提及处理等

3. **GatewayEventHandler** 中的所有方法实现

4. **UserEventHandler** 中的所有方法实现

### 中优先级

1. **autoUploadDrAiData** 相关方法
2. **kickoutAttendeeHandle** 踢出参与者处理
3. **findMulticenter** 多中心查询
4. **getApplyCount** 获取申请数量

### 低优先级

1. 各种自动上传相关方法
2. 隐私协议处理方法
3. 其他辅助工具方法

## 迁移指南

### 迁移步骤

1. **查找原方法**：在原 `eventListenerManager.js` 中找到方法实现
2. **分析依赖**：确定方法依赖的其他方法和属性
3. **迁移实现**：将方法复制到对应的处理器中
4. **更新导入**：添加必要的导入语句
5. **测试验证**：确保方法在新环境中正常工作

### 注意事项

1. **保持API兼容**：确保方法签名和行为与原实现一致
2. **处理依赖**：某些方法可能依赖其他尚未迁移的方法
3. **共享状态**：注意处理器之间的状态共享问题
4. **错误处理**：保持原有的错误处理逻辑

## 测试建议

1. **单元测试**：为每个迁移的方法编写单元测试
2. **集成测试**：测试处理器之间的协作
3. **回归测试**：确保原有功能不受影响
4. **性能测试**：验证重构后的性能表现

## 总结

目前已完成约60%的方法迁移工作，主要集中在ConversationEventHandler中。剩余的工作主要是完善复杂方法的实现和填补其他处理器中的空实现。

建议按优先级逐步完成剩余的迁移工作，确保每个阶段都有可用的功能版本。
