/**
 * 使用示例：如何在项目中使用重构后的EventListenerManager
 */

// 原来的使用方式（在MainScreen.vue或相关组件中）
/*
import EventListenerManager from './lib/eventListenerManager'

// 在组件中
mounted() {
    this.eventManager = new EventListenerManager(this, this.autoLoginManager, this.mainScreenManager)
    this.eventManager.initMainScreenControllerEvent(controller)
}
*/

// 重构后的使用方式
import RefactoredEventListenerManager from './eventHandlers/RefactoredEventListenerManager'

export default {
    name: 'MainScreen',
    data() {
        return {
            eventManager: null
        }
    },
    
    mounted() {
        // 创建重构后的事件管理器
        this.eventManager = new RefactoredEventListenerManager(
            this,                    // Vue实例
            this.autoLoginManager,   // 自动登录管理器
            this.mainScreenManager   // 主屏幕管理器
        )
        
        // 初始化所有事件监听器
        this.eventManager.initMainScreenControllerEvent(this.controller)
    },
    
    beforeDestroy() {
        // 清理资源（如果需要的话）
        if (this.eventManager) {
            // 可以添加清理逻辑
            this.eventManager = null
        }
    },
    
    methods: {
        // 如果需要访问特定的事件处理器，可以通过eventManager访问
        getGatewayHandler() {
            return this.eventManager.gatewayHandler
        },
        
        getUserHandler() {
            return this.eventManager.userHandler
        },
        
        // 示例：手动触发某个处理器的方法
        refreshGroupsetList() {
            this.eventManager.groupsetHandler.getGroupSetList()
        },
        
        // 示例：获取作业数据
        refreshHomeworkData() {
            this.eventManager.homeworkHandler.getUnfinishedHomework(0)
            this.eventManager.homeworkHandler.getUncorrectHomework(0)
            this.eventManager.homeworkHandler.getCorrectedHomework(0)
        }
    }
}

/**
 * 迁移指南：
 * 
 * 1. 替换导入：
 *    将 import EventListenerManager from './lib/eventListenerManager'
 *    改为 import RefactoredEventListenerManager from './lib/eventHandlers/RefactoredEventListenerManager'
 * 
 * 2. 更新实例化：
 *    保持构造函数参数不变，只需要更改类名
 * 
 * 3. API兼容性：
 *    主要的 initMainScreenControllerEvent 方法保持不变
 * 
 * 4. 访问子处理器：
 *    如果需要访问特定功能，可以通过 eventManager.xxxHandler 访问
 * 
 * 5. 测试：
 *    确保所有事件监听功能正常工作
 *    验证各个模块的独立性
 */

/**
 * 优势总结：
 * 
 * 1. 代码组织更清晰：每个处理器专注于特定功能
 * 2. 维护更容易：修改某个功能时只需要关注对应的处理器
 * 3. 测试更方便：可以独立测试每个处理器
 * 4. 扩展更灵活：新增功能时创建新的处理器即可
 * 5. 团队协作更高效：不同开发者可以并行开发不同的处理器
 * 6. 代码复用：通过基类提供通用功能
 * 7. 减少冲突：修改时不会影响其他功能模块
 */
