# EventListenerManager 拆分方案

## 概述

原始的 `EventListenerManager.js` 文件包含1877行代码，处理了main_screen的所有事件监听逻辑。为了提高代码的可维护性和可读性，我们将其按功能模块拆分成多个独立的事件处理器。

## 拆分结构

### 基类
- `BaseEventHandler.js` - 事件处理器基类，提供通用属性和方法

### 具体事件处理器

1. **GatewayEventHandler.js** - 网关事件处理器
   - 处理网关连接、断开、重连等事件
   - 包含方法：gateway_connect, gateway_error, gateway_reconnecting 等

2. **UserEventHandler.js** - 用户事件处理器
   - 处理用户信息更新、好友申请等事件
   - 包含方法：userResponseFriendApply, userAddFriend, update_user_info 等

3. **ConversationEventHandler.js** - 对话事件处理器（合并版）
   - 处理对话、群组、资源和通知相关事件
   - 包含方法：notify_start_conversation, receive_group_message, notify_delete_group, notify.group.resource.delete.exam, notify_exception 等

4. **LiveEventHandler.js** - 直播事件处理器
   - 处理直播开始/停止、录制等事件
   - 包含方法：notify_agora_live_start, notify_agora_live_stop 等

5. **HomeworkEventHandler.js** - 作业事件处理器
   - 处理作业相关事件
   - 包含方法：student_answer_sheet_update, teacher_answer_sheet_update 等

6. **OtherEventHandler.js** - 其他事件处理器（合并版）
   - 处理媒体传输和设备相关事件
   - 包含方法：notify_update_media_transfer_task, equipment_server_device_alram_update 等

7. **ServerInfoEventHandler.js** - 服务器信息事件处理器
   - 处理服务器配置相关事件
   - 包含方法：server_info 等

### 协调器
- `RefactoredEventListenerManager.js` - 重构后的主管理器，作为各个事件处理器的协调器

## 合并说明

根据功能相关性，我们进行了以下合并：

1. **ConversationEventHandler.js** 合并了：
   - 原 ConversationEventHandler（对话事件）
   - 原 GroupsetEventHandler（群组事件）
   - 原 ResourceEventHandler（资源事件）
   - 原 NotificationEventHandler（通知事件）

2. **OtherEventHandler.js** 合并了：
   - 原 MediaTransferEventHandler（媒体传输事件）
   - 原 DeviceEventHandler（设备事件）

这样的合并使得相关功能更加集中，减少了文件数量，同时保持了良好的功能分离。

## 优势

1. **代码分离** - 每个处理器专注于特定功能领域
2. **易于维护** - 修改某个功能时只需要关注对应的处理器
3. **可测试性** - 每个处理器可以独立进行单元测试
4. **可扩展性** - 新增功能时可以创建新的处理器
5. **代码复用** - 通过基类提供通用功能

## 使用方式

```javascript
import RefactoredEventListenerManager from './eventHandlers/RefactoredEventListenerManager'

// 创建管理器实例
const eventManager = new RefactoredEventListenerManager(vueInstance, autoLoginManager, mainScreenManager)

// 初始化事件监听
eventManager.initMainScreenControllerEvent(controller)
```

## 实施状态

✅ **已完成**：
1. 创建基类和各个事件处理器的框架
2. 实现所有12个事件处理器的基本结构
3. 创建重构后的主管理器
4. 编写使用示例和迁移指南

🔄 **待完善**：
1. 将原文件中的具体方法实现迁移到对应的处理器中
2. 完善各处理器之间的依赖关系
3. 更新所有导入和依赖关系
4. 进行全面测试确保功能正常

## 迁移步骤

1. ✅ 创建基类和各个事件处理器的框架
2. 🔄 逐步将原文件中的方法迁移到对应的处理器中
3. 🔄 更新导入和依赖关系
4. 🔄 测试确保功能正常
5. 🔄 替换原有的EventListenerManager使用

## 注意事项

- 某些方法可能被多个处理器使用，需要考虑放在基类或工具类中
- 保持原有的事件监听逻辑不变，只是重新组织代码结构
- 确保所有依赖的导入都正确更新
