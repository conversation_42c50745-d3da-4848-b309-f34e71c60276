import BaseEventHandler from './BaseEventHandler'
import { resumeAllTasks } from '@/common/oss'

/**
 * 网关事件处理器
 * 处理网关连接、断开、重连等相关事件
 */
class GatewayEventHandler extends BaseEventHandler {
    /**
     * 初始化网关相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on('gateway_connect', () => {
            this.autoLoginManager.onSocketConnectSuccess()
            this.mainScreenManager.onSocketConnectSuccess()
            this.getAllTags()

            this.getMultiCenterOptionList()
            this.getDeviceNameById()
            this.getGroupSetList()
            this.getAiAnalyzeTypes()
            this.vm.updateLiveCount()
            controller.emit("get_consultation_image_list", {
                start: 0,
                count: this.vm.systemConfig.consultationImageShowNum
            }, this.setConsultationImageList.bind(this))

            controller.emit('get_all_hospital_name', this.setAllHospital.bind(this))
            controller.emit('get_user_info')
            controller.emit('get_version_info')
            controller.emit("init_main_screen_controller_event", controller)

            setTimeout(() => {
                resumeAllTasks()
            }, 2000)

            if (this.vm.user.pacsCid) {
                this.vm.joinAndStartConversation(this.vm.user.pacsCid)
            }

            this.vm.$refs['user_avatar'].ifCreateUserAvatar()
            this.vm.$refs['init_organization'].init()
        })

        controller.on("gateway_error", (data) => {
            this.vm.resetApp()
            this.autoLoginManager.onSocketError(data)
        })

        controller.on("gateway_reconnecting", () => {
            this.mainScreenManager.onSocketReconnecting()
        })

        controller.on("gateway_reconnect_fail", (data) => {
            this.autoLoginManager.onSocketReconnectFail()
            this.mainScreenManager.onSocketReconnectFail(data)
        })

        controller.on("gateway_reconnect", () => {
            this.mainScreenManager.onSocketReconnect()
        })

        controller.on("gateway_disconnect", (data) => {
            this.autoLoginManager.onSocketDisconnect(data)
            this.mainScreenManager.onSocketDisconnect(data)
        })
    }

    // 以下方法需要从原EventListenerManager中移动过来
    getAllTags() {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    getMultiCenterOptionList() {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    getDeviceNameById() {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    getGroupSetList() {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    getAiAnalyzeTypes() {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setConsultationImageList(is_succ, data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setAllHospital(is_succ, data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }
}

export default GatewayEventHandler
